import { ExtendedFormValues } from "@/types/types";

export const ACCESS_TOKEN_KEY = "__ATK__";

export const EMAIL_REGEX = /^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/;

export const PASSWORD_REGEX = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;

export const MAX_IMAGE_SIZE = 5242880;

export const ScheduleInterviewformSubmissionType = {
  SCHEDULE: "schedule",
  UPDATE: "update",
};
export const S3_PATHS = {
  PROFILE_IMAGE: "profile-images/:path",
};

export const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [
  "Arrive at the interview location on time with a government-issued ID.",
  "Ensure your phone is on silent mode and distractions are minimized.",
  "Bring a printed copy of your resume and any supporting documents.",
  "Dress professionally and maintain proper body language.",
  "Listen carefully, answer honestly, and ask for clarification if needed.",
  "Respect the interview flow and do not interrupt the interviewer.",
  "Take brief notes if necessary, but focus on active conversation.",
  "If you need assistance or face any issues, notify the interview coordinator.",
];

export const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [
  "Join the interview on time using the link provided.",
  "Ensure a stable internet connection and a quiet, well-lit space.",
  "Test your camera, microphone, and audio settings in advance.",
  "Keep your video on unless instructed otherwise by the interviewer.",
  "Minimize background noise and avoid multitasking during the session.",
  "Use headphones if possible for better audio clarity.",
  "Be attentive, respond clearly, and maintain professional posture.",
  "Contact support if you face technical difficulties before or during the interview.",
];

/**
 * Permission Constants
 */
export const PERMISSION = {
  CREATE_OR_EDIT_JOB_POST: "create-or-edit-job-post",
  SCHEDULE_CONDUCT_INTERVIEWS: "schedule-conduct-interviews",
  VIEW_HIRED_CANDIDATES: "view-hired-candidates",
  ARCHIVE_RESTORE_CANDIDATES: "archive-restore-candidates",
  ARCHIVE_RESTORE_JOB_POSTS: "archive-restore-job-posts",
  MANUAL_RESUME_SCREENING: "manual-resume-screening",
  EDIT_SCHEDULED_INTERVIEWS: "edit-scheduled-interviews",
  ADD_ADDITIONAL_CANDIDATE_INFO: "add-additional-candidate-info",
  ADD_OR_EDIT_INTERVIEW_NOTES: "add-or-edit-interview-notes",
  MANAGE_TOP_CANDIDATES: "manage-top-candidates",
  MANAGE_PRE_INTERVIEW_QUESTIONS: "manage-pre-interview-questions",
  CREATE_FINAL_ASSESSMENT: "create-final-assessment",
  VIEW_FINAL_ASSESSMENT: "view-final-assessment",
  VIEW_CANDIDATE_PROFILE_SUMMARY: "view-candidate-profile-summary",
  HIRE_CANDIDATE: "hire-candidate",
  CREATE_NEW_ROLE: "create-new-role",
  MANAGE_USER_PERMISSIONS: "manage-user-permissions",
  CREATE_NEW_DEPARTMENT: "create-new-department",
  ADD_INTERVIEW_PARTICIPANTS: "add-interview-participants",
  VIEW_SUBSCRIPTION_PLAN: "view-subscription-plan",
  MANAGE_SUBSCRIPTIONS: "manage-subscriptions",
  VIEW_AUDIT_LOGS_UPCOMING: "view-audit-logs-upcoming",
  VIEW_ALL_SCHEDULED_INTERVIEWS: "view-all-scheduled-interviews",
};

/**
 * Skill Constants
 */
export const SKILL_CONSTANTS = {
  REQUIRED_ROLE_SKILLS: 10,
  REQUIRED_CULTURE_SKILLS: 5,
};
export const commonConstants = {
  finalAssessmentId: "finalAssessmentId",
  token: "token",
  isShared: "isShared",
  isSubmitted: "isSubmitted",
  jobId: "jobId",
  jobApplicationId: "jobApplicationId",
};

export const QuestionType = {
  MCQ: "mcq",
  TRUE_FALSE: "true_false",
};

// Constants for option IDs
export const OPTION_ID = {
  A: "A",
  B: "B",
  C: "C",
  D: "D",
  TRUE: "true",
  FALSE: "false",
} as const;

// Constants for question types
export const QUESTION_TYPE = {
  MCQ: "mcq" as const,
  TRUE_FALSE: "true_false" as const,
};

// Constants for default options
export const DEFAULT_MCQ_OPTIONS = [
  { id: OPTION_ID.A, text: "" },
  { id: OPTION_ID.B, text: "" },
  { id: OPTION_ID.C, text: "" },
  { id: OPTION_ID.D, text: "" },
];

export const DEFAULT_TRUE_FALSE_OPTIONS = [
  { id: OPTION_ID.TRUE, text: "True" },
  { id: OPTION_ID.FALSE, text: "False" },
];

export const INTERVIEW_SCHEDULE_ROUND_TYPE = [
  {
    label: "One-On-One",
    value: "One-On-One",
  },
  {
    label: "Video Call",
    value: "Video Call",
  },
];

/**
 * Interview Question Types
 */
export const QUESTION_TYPES = {
  ROLE_SPECIFIC: "role_specific",
  CULTURE_SPECIFIC: "culture_specific",
  CAREER_BASED: "career_based",
} as const;

export type QuestionType = (typeof QUESTION_TYPES)[keyof typeof QUESTION_TYPES];
/**
 * Empty Content Patterns
 */
export const EMPTY_CONTENT_PATTERNS = ["<p><br></p>", "<p></p>", "<div><br></div>", "<div></div>", "<p>&nbsp;</p>"];

// Define the initial state using FormValues type
export const initialState: ExtendedFormValues = {
  title: "",
  employment_type: "",
  department_id: "",
  salary_range: "",
  salary_cycle: "",
  location_type: "",
  state: "",
  city: "",
  role_overview: "",
  experience_level: "",
  responsibilities: "",
  educations_requirement: "",
  certifications: undefined,
  skills_and_software_expertise: "",
  experience_required: "",
  ideal_candidate_traits: "",
  about_company: "",
  perks_benefits: undefined,
  tone_style: "",
  additional_info: undefined,
  compliance_statement: [],
  show_compliance: false,
  hiring_type: "",
};

// Define the skill item interface
export interface ISkillItem {
  id: number;
  title: string;
  description: string;
  short_description: string;
}

// Define a skill category interface
export interface ISkillCategory {
  type: string;
  items: ISkillItem[];
}

// Define the slice state type
export interface AllSkillsState {
  categories: ISkillCategory[];
  loading: boolean;
  error: string | null;
}

export const FILE_EXTENSION = [
  "pdf",
  "plain",
  "csv",
  "vnd.ms-excel.sheet.macroEnabled.12",
  "vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "vnd.openxmlformats-officedocument.wordprocessingml.document",
  "vnd.openxmlformats-officedocument.presentationml.presentation",
];

export const ACTIVE = "active";
export const TOKEN_EXPIRED = "Session Expired! Please log in again.";
export const DEFAULT_LIMIT = 15;

export const IMAGE_EXTENSIONS = ["png", "jpg", "jpeg", "gif", "webp"];

export const ASSESSMENT_INSTRUCTIONS = {
  instructions: [
    "Do not refresh or close the browser",
    "Check your internet connection",
    "Ensure a distraction-free environment",
    "Click 'Submit' only once when finished",
    "Read each question carefully",
    "Manage your time efficiently",
    "Avoid any form of plagiarism",
    "Reach out to support if needed",
  ],
};
export const PERMISSIONS_COOKIES_KEY = "permissions_data";

export const PDF_FILE_NAME = "pdf";
export const PDF_FILE_TYPE = "application/pdf";
export const PDF_FILE_SIZE_LIMIT = 5 * 1024 * 1024;
export const PDF_ADDITIONAL_SUBMISSION_LIMIT = 10854484;
