import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import SubscriptionPlanModel from "./subscription_plans";
import SubscriptionPricingModel from "./subscription_pricing";

/* eslint-disable no-unused-vars */
enum SubscriptionType {
  MONTHLY = "Monthly",
  QUARTERLY = "Quarterly",
  YEARLY = "Yearly",
}

enum SubscriptionStatus {
  ACTIVE = "active",
  PAST_DUE = "past_due",
  CANCELED = "canceled",
  INCOMPLETE = "incomplete",
  CANCEL_AT_PERIOD_END = "cancel_at_period_end",
  EXPIRED = "expired",
}

@Entity("organization_subscriptions")
class OrganizationSubscriptionModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "organization_id", nullable: false })
  organizationId: number;

  @ManyToOne(() => SubscriptionPlanModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "subscription_id" })
  subscription: SubscriptionPlanModel;

  @Column({ name: "subscription_id", nullable: false })
  subscriptionId: number;

  @ManyToOne(() => SubscriptionPricingModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "subscription_pricing_id" })
  subscriptionPricing: SubscriptionPricingModel;

  @Column({ name: "subscription_pricing_id", nullable: false })
  subscriptionPricingId: number;

  @Column({
    type: "enum",
    enum: SubscriptionType,
    name: "subscription_type",
    nullable: false,
  })
  subscriptionType: SubscriptionType;

  @Column({ name: "is_active", default: true })
  isActive: boolean;

  @Column({ name: "start_date", type: "date", nullable: false })
  startDate: Date;

  @Column({ name: "expiry_date", type: "date", nullable: false })
  expiryDate: Date;

  @Column({ name: "next_billing_date", type: "date", nullable: true })
  nextBillingDate: Date;

  @Column({
    name: "stripe_subscription_id",
    type: "varchar",
    length: 50,
    nullable: false,
  })
  stripeSubscriptionId: string;

  @Column({
    type: "enum",
    enum: SubscriptionStatus,
    name: "status",
    default: SubscriptionStatus.ACTIVE,
  })
  status: SubscriptionStatus;

  @Column({ name: "cancellation_date", type: "date", nullable: true })
  cancellationDate: Date;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}

export default OrganizationSubscriptionModel;
