import * as Sentry from "@sentry/node";
import sendMail from "./sendgrid";
import { INTERVIEW_EMAIL_TYPE } from "./constants";

// eslint-disable-next-line import/prefer-default-export
export const sendInterviewEmail = async ({
  interviewerEmail,
  candidateEmail,
  candidateName,
  position,
  interviewType,
  date,
  duration,
  interviewRound,
  meetingLink,
  resume,
  interviewerName,
  previousInterviewerName,
  previousInterviewerEmail,
  orgName,
  type,
  previousDate,
}: {
  interviewerEmail: string;
  candidateEmail: string;
  candidateName: string;
  position: string;
  interviewType: string;
  date: string;
  orgName: string;
  duration: number;
  interviewRound: number;
  meetingLink: string;
  resume: string;
  interviewerName: string;
  previousInterviewerName?: string;
  previousInterviewerEmail?: string;
  type: string;
  previousDate?: string;
}) => {
  try {
    const interviewerEmailTemplate = `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Scheduled - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <!-- Main Container -->
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Logo Section -->
        <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
                <img src="./logo-s9.svg" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
            </td>
        </tr>

        <!-- Heading Section -->
        <tr>
            <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                Interview Scheduled: <br> <span style="color: #3182ce; font-weight: 600; font-size: 18px; line-height: 1;">${candidateName}</span> <br> <span style="color: #3182ce; font-weight: 600; font-size: 18px; line-height: 1;">${position}</span>
            </td>
        </tr>

        <!-- Email Content -->
        <tr>
            <td style="padding: 30px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Hi <span
                        style="color: #3182ce; font-weight: 600;">${interviewerName}</span>,</p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    You have been scheduled to conduct an interview for the following candidate:
                </p>

                <!-- Interview Details Box -->
                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                    style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                    <tr>
                        <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                            <strong>Candidate:</strong> ${candidateName}<br>
                            <strong>Position:</strong> ${position}<br>
                            <strong>Interview Type:</strong> ${interviewType}<br>
                            <strong>Date & Time:</strong> ${new Date(date).toLocaleString()}<br>
                            <strong>Duration:</strong> ${duration}<br>
                            <strong>Interview Round:</strong> ${interviewRound}<br>
                            <strong>Meeting Link:</strong> <a href="${meetingLink || ""}" style="color: #3182ce; text-decoration: none;">${meetingLink || ""}</a><br>
                            <strong>Resume & Documents:</strong> <a href="${resume || ""}" style="color: #3182ce; text-decoration: none;">${resume || ""}</a>
                        </td>
                    </tr>
                </table>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Please take a moment to review the candidate's resume and suggested interview questions prior to the session. You may add your own questions or notes during the interview as well.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Let us know if you face any issues accessing the interview link or materials.
                </p>

                <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                    <strong>Best regards,</strong><br>
                    ${orgName}<br>
                </p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td
                style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                © 2023 STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>

</html>`;

    const candidateEmailTemplate = `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Scheduled - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <!-- Main Container -->
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Logo Section -->
        <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
                <img src="./logo-s9.svg" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
            </td>
        </tr>

        <!-- Heading Section -->
        <tr>
            <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                Interview Scheduled <br> <span style="color: #3182ce; font-weight: 600; font-size: 18px; line-height: 1;">${position}</span> <br> <span style="color: #3182ce; font-weight: 600; font-size: 18px; line-height: 1;">${"Stratum9"}</span>
            </td>
        </tr>

        <!-- Email Content -->
        <tr>
            <td style="padding: 30px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Hi <span
                        style="color: #3182ce; font-weight: 600;">${candidateName}</span>,</p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We are pleased to inform you that your <span style="color: #3182ce; font-weight: 600;">${interviewType}</span> interview for the position of <span style="color: #3182ce; font-weight: 600;">${position}</span> at <span style="color: #3182ce; font-weight: 600;">${"Stratum9"}</span> has been
                    scheduled.
                </p>

                <p style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; line-height: 1.5;">
                    📌 Interview Details:
                </p>

                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                    style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                    <tr>
                        <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                            🗓 <strong>Date & Time:</strong> ${new Date(date).toLocaleString()}<br>
                            🧑‍💼 <strong>Interviewer(s):</strong> ${interviewerName}<br>
                            ⏱ <strong>Expected Duration:</strong> ${duration}
                        </td>
                    </tr>
                </table>

                <p style="margin: 0 0 10px 0; font-size: 16px; font-weight: bold; line-height: 1.5;">
                    🔔 Please bring the following with you:
                </p>

                <ul style="margin: 0 0 20px 0; padding-left: 20px; font-size: 16px; line-height: 1.5;">
                    <li>A <span style="color: #333333; font-weight: 600;">printed copy of your resume</span></li>
                    <li>Your <span style="color: #333333; font-weight: 600;">laptop</span> or any <span style="color: #333333; font-weight: 600;">work samples</span> you'd like to showcase during the interview</li>
                    <li>A valid ID for security check-in</li>
                </ul>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Make sure to arrive 10 minutes early to allow for check-in and settling in. If you have any
                    questions or require special accommodations, feel free to reach out.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We look forward to meeting you in person!
                </p>

                <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                    <strong>Best regards,</strong><br>
                    ${orgName}
                </p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td
                style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                © 2023 STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>

</html>`;

    const changeInterviewerTemplate = `<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interview Update - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
  <!-- Main Container -->
  <table width="100%" border="0" cellspacing="0" cellpadding="0"
    style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
    <!-- Logo Section -->
    <tr>
      <td align="center" style="padding:20px 0 0; background-color: #fff;">
        <img src="./logo-s9.svg" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
      </td>
    </tr>

    <!-- Heading Section -->
    <tr>
      <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
        Interview Update – Change in Interview Assignment
      </td>
    </tr>

    <!-- Email Content -->
    <tr>
      <td style="padding: 30px;">
        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
          Dear <span style="color: #3182ce; font-weight: 600;">${previousInterviewerName}</span>,
        </p>

        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
          I hope you're doing well.
        </p>

        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
          Please note that the interview with <strong>${candidateName}</strong> for the <strong>${position}</strong> role, originally scheduled on <strong>${new Date(previousDate).toLocaleString()}</strong> has now been reassigned to another interviewer.
        </p>

        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
          Thank you for your time and understanding.
        </p>

        <p style="margin: 0; font-size: 16px; line-height: 1.5;">
          <strong>Best regards,</strong><br>
          ${orgName}
        </p>
      </td>
    </tr>

    <!-- Footer -->
    <tr>
      <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
        © 2023 STRATUM 9. All rights reserved.
      </td>
    </tr>
  </table>
</body>

</html>
`;

    const subject = "Interview Scheduled";

    let candidateResponse;
    let interviewerResponse;

    if (type === INTERVIEW_EMAIL_TYPE.SCHEDULE) {
      await sendMail({
        email: interviewerEmail,
        subject,
        textContent: subject,
        htmlContent: interviewerEmailTemplate,
      });

      await sendMail({
        email: candidateEmail,
        subject,
        textContent: subject,
        htmlContent: candidateEmailTemplate,
      });
    } else {
      await sendMail({
        email: previousInterviewerEmail,
        subject: "Interview Update – Change in Interview Assignment",
        textContent: "Interview Update – Change in Interview Assignment",
        htmlContent: changeInterviewerTemplate,
      });

      await sendMail({
        email: interviewerEmail,
        subject,
        textContent: subject,
        htmlContent: interviewerEmailTemplate,
      });
    }

    return { interviewerResponse, candidateResponse };
  } catch (error) {
    Sentry.captureException(error);

    return { error: "Email sending failed" };
  }
};
