@use "./abstracts" as *;

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background: white;
  padding: 25px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.formGroup {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }

  input,
  select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
  }
}

.timeGroup {
  display: flex;
  gap: 16px;

  .formGroup {
    flex: 1;
  }
}

.timeInputContainer {
  display: flex;
  gap: 8px;

  input {
    flex: 3;
  }

  select {
    flex: 1;
  }
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;

  button {
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
  }
}

.saveButton {
  background-color: #4285f4;
  color: white;
  border: none;
}

.cancelButton {
  background-color: transparent;
  border: 1px solid #ddd;
}
