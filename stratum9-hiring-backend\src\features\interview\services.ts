/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */

import * as Sen<PERSON> from "@sentry/node";
import { Brackets, In } from "typeorm";
import <PERSON><PERSON><PERSON> from "openai";

import envConfig from "../../config/envConfig";
import {
  API_RESPONSE_MSG,
  DEFAULT_LIMIT,
  DEFAULT_OFFSET,
  GPT_MODEL,
  INTERVIEW_EMAIL_TYPE,
  INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS,
  MAX_HOURS_BETWEEN_START_AND_END_TIME,
  ONE_TO_ONE_INTERVIEW_INSTRUCTIONS,
  REDIS_KEYS,
  SCHEDULE_INTERVIEW_MINUTES_MS,
  SCHEDULE_INTERVIEW_ONE_MONTH_MS,
  STRATUM_POINT_DESCRIPTION,
  VIDEO_CALL_INTERVIEW_INSTRUCTIONS,
} from "../../utils/constants";

import <PERSON><PERSON>odel from "../../schema/s9-innerview/interview";
import dbConnection from "../../db/dbConnection";
import {
  GetInterviewsData,
  IAddInterviewSkillQuestion,
  IGenerateInterviewSkillQuestions,
  IGetCandidateList,
  IGetInterviewSkillQuestions,
  IGetJobList,
  IUpdateInterviewAnswers,
  IUpdateInterviewSkillQuestion,
  ScheduleInterviewData,
  IEndInterview,
} from "./interface";
import Employee from "../../schema/s9-innerview/employees";
import UserModel from "../../schema/s9/user";
import JobApplicationsModel, {
  Status,
} from "../../schema/s9-innerview/job_applications";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import { JobsModel } from "../../schema/s9-innerview/jobs";
import InterviewSkillQuestionsAnswersModel, {
  SourceType,
} from "../../schema/s9-innerview/interview_skill_questions_answers";
import JobSkillsModel, {
  SkillType,
} from "../../schema/s9-innerview/job_skills";
import InterviewSkillEvaluationModel from "../../schema/s9-innerview/interview_skill_evaluations";
import { sendInterviewEmail } from "../../utils/interviewEmail";
import AuthServices from "../auth/services";
import OrganizationModel from "../../schema/s9/organization";
import Cache from "../../db/cache";

const CONFIG = envConfig();
/**
 * Constants for OpenAI API and caching
 */
const OPENAI_API_KEY = CONFIG.openai_api_key;

export class InterviewServices {
  static conductInterviewStaticInformation = async () => {
    try {
      const cache = new Cache();
      const cachedData = await cache.get(
        REDIS_KEYS.CONDUCT_INTERVIEW_INFORMATION
      );
      if (cachedData) {
        return {
          success: true,
          data: JSON.parse(cachedData),
          message: API_RESPONSE_MSG.success,
        };
      }

      const data = {
        oneToOneInterviewInstructions: ONE_TO_ONE_INTERVIEW_INSTRUCTIONS,
        videoCallInterviewInstructions: VIDEO_CALL_INTERVIEW_INSTRUCTIONS,
        startumDescription: STRATUM_POINT_DESCRIPTION,
      };
      await cache.set(
        REDIS_KEYS.CONDUCT_INTERVIEW_INFORMATION,
        JSON.stringify(data)
      );
      return {
        success: true,
        data,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      return { success: false, message: error.message };
    }
  };

  static getJobList = async ({ orgId, searchString }: IGetJobList) => {
    try {
      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);

      let query = jobRepo
        .createQueryBuilder("job")
        .where("job.orgId = :orgId", { orgId })
        .andWhere("job.isActive = true")
        .groupBy(
          "job.id, job.job_id, job.title, job.createdTs, job.isActive, job.finalJobDescriptionHtml"
        )
        .select([
          "job.id as value",
          "job.title as label",
          "job.jobId as jobId",
        ]);

      if (searchString && searchString.trim() !== "") {
        const searchTerm = `%${searchString.toLowerCase().trim()}%`;
        query = query
          .andWhere("job.title like :searchTitle", {
            searchTitle: searchTerm,
          })
          .limit(DEFAULT_LIMIT);
      } else {
        query = query.limit(5);
      }

      const jobList = await query.getRawMany();
      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: jobList,
      };
    } catch (error) {
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getCandidateList = async ({
    orgId,
    jobId,
    searchString,
  }: IGetCandidateList) => {
    try {
      const jobApplicationRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          JobApplicationsModel
        );

      let query = jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .leftJoinAndSelect("jobApplication.candidate", "candidate")
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.jobId = :jobId", { jobId })
        .andWhere("jobApplication.isActive = true")
        .andWhere("jobApplication.status = 'Approved'")
        .select(["jobApplication.id as value", "candidate.name as label"]);

      if (searchString && searchString.trim() !== "") {
        const searchTerm = `%${searchString.toLowerCase().trim()}%`;
        query = query
          .andWhere("candidate.name like :searchCandidateName", {
            searchCandidateName: searchTerm,
          })
          .limit(DEFAULT_LIMIT);
      } else {
        query = query.limit(5);
      }

      const candidateList = await query.getRawMany();
      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: candidateList,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.log("getCandidateList=======>>>>", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getInterviewsByFilters = async ({
    monthYear,
    jobId,
    applicationId,
    userId,
  }: {
    monthYear: string;
    jobId?: number;
    applicationId?: number;
    userId?: number;
  }) => {
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const [month, year] = monthYear
        ? monthYear.split("-")
        : [new Date().getMonth() + 1, new Date().getFullYear()];

      const interviewQuery = interviewRepo
        .createQueryBuilder("interview")
        .leftJoinAndSelect("interview.job", "job")
        .leftJoinAndSelect("interview.jobApplication", "jobApplication")
        .leftJoinAndSelect("jobApplication.candidate", "candidate")
        .where("MONTH(interview.startTime) = :month", { month })
        .andWhere("YEAR(interview.startTime) = :year", { year });

      if (userId) {
        interviewQuery.andWhere(
          "(interview.interviewerId = :userId OR interview.scheduledBy = :userId)",
          {
            userId,
          }
        );
      }

      if (jobId) {
        interviewQuery.andWhere("interview.jobId = :jobId", { jobId });
      }

      if (applicationId) {
        interviewQuery.andWhere("interview.jobApplicationId = :applicationId", {
          applicationId,
        });
      }

      const interviews = await interviewQuery
        .select([
          "interview.id as id",
          "interview.title as title",
          "interview.startTime as start",
          "interview.endTime as end",
          "interview.roundNumber as roundNumber",
          "interview.roundType as roundType",
          "interview.isEnded as isEnded",
          "interview.scheduleAt as scheduleAt",
          "interview.jobApplicationId as jobApplicationId",
          "interview.attachments as attachments",
          "interview.interviewerId as interviewerId",
          "interview.description as description",
          "jobApplication.resumeFile as resumeLink",
          "candidate.name as candidateName",
          "job.title as jobTitle",
          "job.job_id as jobUniqueId",
          "job.id as jobId",
        ])
        .getRawMany();

      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);

      // Collect all unique interviewerIds
      const interviewerIds = [
        ...new Set(interviews.map((i) => i.interviewerId)),
      ];

      // Fetch all users in one query
      const users = await userRepo.find({
        where: { id: In(interviewerIds) },
        select: ["id", "first_name", "last_name"],
      });

      console.log("getInterviews users===>>>", users);

      // Map users by id for quick lookup
      const userMap = new Map(
        users.map((user) => [user.id, `${user.first_name} ${user.last_name}`])
      );

      // Merge user info into interviews
      const interviewsInfo = interviews.map((interview) => {
        const userName = userMap.get(+interview.interviewerId);

        return {
          ...interview,
          interviewerName: userName || "",
        };
      });

      // console.log("====interviewsInfo", interviewsInfo)

      return interviewsInfo;
    } catch (error) {
      console.log("Interviews error===>>>", error);
      return [];
    }
  };

  static getMyInterviews = async (data: {
    userId: number;
    monthYear: string;
  }) => {
    try {
      const { monthYear, userId } = data;

      const interviews = await this.getInterviewsByFilters({
        monthYear,
        userId,
      });

      console.log("====interviews", interviews);

      return {
        success: true,
        message: API_RESPONSE_MSG.interviews_fetched,
        data: interviews,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.log("getInterviews error===>>>", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getInterviews = async (data: GetInterviewsData) => {
    try {
      const { jobId, applicationId, monthYear } = data;

      console.log("data===>>>>", data);

      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);

      const job = await jobRepo.findOne({ where: { id: +jobId } });

      if (!job) {
        return {
          success: false,
          message: API_RESPONSE_MSG.job_not_found,
        };
      }

      const interviews = await this.getInterviewsByFilters({
        monthYear,
        jobId: +jobId,
        applicationId: +applicationId,
      });

      return {
        success: true,
        message: API_RESPONSE_MSG.interviews_fetched,
        data: interviews,
      };
    } catch (error) {
      Sentry.captureException(error);
      console.log("getInterviews error===>>>", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getInterviewers = async (data: {
    orgId: number;
    jobId: number;
    searchString: string;
  }) => {
    const { orgId, jobId, searchString } = data;

    console.log("data===>>>>", data);
    try {
      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const employeeRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(Employee);

      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);

      const job = await jobRepo.findOne({ where: { id: +jobId } });

      if (!job) {
        return {
          success: false,
          message: API_RESPONSE_MSG.job_not_found,
        };
      }

      console.log(job?.departmentId, "searchString===>>>", searchString);

      const employeesWithRoles = await employeeRepo
        .createQueryBuilder("employee")
        .leftJoinAndSelect("employee.role", "role")
        .where("employee.organizationId = :orgId", { orgId })
        .orderBy({
          "CASE WHEN employee.departmentId = :departmentId THEN 0 ELSE 1 END":
            "ASC",
          "employee.interviewOrder": "ASC",
        })
        .setParameter("departmentId", job?.departmentId)
        .select(["employee.userId as userId", "role.name as roleName"])
        .getRawMany();

      console.log("employeesWithRoles===>>>", employeesWithRoles);

      if (employeesWithRoles.length === 0) {
        return {
          success: true,
          message: API_RESPONSE_MSG.interviewers_fetched,
          data: [],
        };
      }

      const employeeUserIdsList = employeesWithRoles.map(
        (employee) => employee.userId
      );

      console.log("employeeUserIdsList===>>>", employeeUserIdsList);

      const userIdToRoleMap = new Map(
        employeesWithRoles.map((row) => [row.userId, row.roleName])
      );

      console.log("userIdToRoleMap===>>>", userIdToRoleMap);

      let query = userRepo
        .createQueryBuilder("")
        .select(["id", "first_name", "last_name", "email"])
        .where("id IN (:...userIds)", { userIds: employeeUserIdsList });

      if (searchString && searchString.trim() !== "") {
        const searchTerm = `%${searchString.toLowerCase().trim()}%`;
        query = query.andWhere(
          "CONCAT(first_name, ' ', last_name) like :searchFullName",
          {
            searchFullName: searchTerm,
          }
        );
      }

      // Get all matching users first
      const allUsers = await query.getRawMany();

      // Sort users based on the employeeUserIdsList order
      const sortedUsers = employeeUserIdsList
        .map((userId) => allUsers.find((user) => +user.id === +userId))
        .filter(Boolean); // Remove any undefined entries

      // Apply limit after sorting
      const users = sortedUsers.slice(
        0,
        searchString && searchString.trim() !== "" ? 10 : 5
      );

      console.log("users===>>>", users);

      const filteredUsers = users.map((user) => ({
        label: `${user.first_name} ${user.last_name} (${userIdToRoleMap.get(+user.id) || ""})`,
        value: user.id,
      }));

      return {
        success: true,
        message: API_RESPONSE_MSG.interviewers_fetched,
        data: filteredUsers,
      };
    } catch (error) {
      console.log("getInterviewers error===>>>", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static async prepareAndSendInterviewEmail({
    jobId,
    candidateId,
    interviewerId,
    startTimeDate,
    endTimeDate,
    roundType,
    roundNumber,
    fileUrlArray,
    type,
    previousDate = null,
    previousInterviewerName = null,
    previousInterviewerEmail = null,
  }: {
    jobId: number;
    candidateId: number;
    interviewerId: number;
    startTimeDate: Date;
    endTimeDate: Date;
    roundType: string;
    roundNumber: number;
    fileUrlArray: string;
    type: string;
    previousDate?: string;
    previousInterviewerName?: string;
    previousInterviewerEmail?: string;
  }) {
    try {
      const jobRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobsModel);
      const job = await jobRepo.findOne({
        where: { id: +jobId },
        select: ["title", "orgId"],
      });

      const orgRepo =
        await dbConnection.getS9DatabaseRepository(OrganizationModel);
      const orgInfo = await orgRepo.findOne({
        where: { id: job.orgId },
        select: ["name"],
      });

      const candidateRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(CandidatesModel);
      const candidate = await candidateRepo.findOne({
        where: { id: +candidateId },
        select: ["name", "email"],
      });

      const interviewerInfo = await AuthServices.getUserByUserId(interviewerId);

      const emailParams = {
        interviewerEmail: interviewerInfo.email,
        candidateEmail: candidate.email,
        candidateName: candidate.name,
        position: job?.title,
        interviewType: roundType,
        date: startTimeDate.toDateString(),
        duration:
          (new Date(endTimeDate).getTime() -
            new Date(startTimeDate).getTime()) /
          (1000 * 60),
        interviewRound: roundNumber,
        meetingLink: "",
        resume: fileUrlArray?.length ? JSON.parse(fileUrlArray)[0] : "",
        interviewerName: `${interviewerInfo.first_name} ${interviewerInfo.last_name}`,
        orgName: orgInfo.name,
        type,
        previousDate,
        previousInterviewerName,
        previousInterviewerEmail,
      };

      const { interviewerResponse, candidateResponse } =
        await sendInterviewEmail(emailParams);

      console.log("interviewerResponse===>>>", interviewerResponse);
      console.log("candidateResponse===>>>", candidateResponse);

      return { job, interviewerInfo };
    } catch (error) {
      console.error("Error sending interview email:", error);
      Sentry.captureException(error);
      return { error };
    }
  }

  static overlappingInterviews = async (
    jobApplicationId: number,
    jobId: number,
    roundType: string,
    startTime: Date,
    endTime: Date,
    interviewId?: number
  ) => {
    const interviewRepo =
      await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

    const query = interviewRepo
      .createQueryBuilder("interview")
      .andWhere("interview.jobApplicationId = :jobApplicationId", {
        jobApplicationId,
      })
      .andWhere("interview.jobId = :jobId", { jobId })
      .andWhere("interview.roundType = :roundType", { roundType })
      .andWhere(
        "((:startTime BETWEEN interview.startTime AND interview.endTime) OR " +
          "(:endTime BETWEEN interview.startTime AND interview.endTime) OR " +
          "(interview.startTime <= :startTime AND interview.endTime >= :endTime))",
        { startTime, endTime }
      );

    // Exclude the current interview when updating
    if (interviewId) {
      query.andWhere("interview.id != :interviewId", { interviewId });
    }

    const overlappingInterviewsCount = await query.getCount();
    return overlappingInterviewsCount;
  };

  static updateOrScheduleInterview = async (
    data: ScheduleInterviewData,
    userId: number
  ) => {
    const {
      title,
      jobId,
      interviewerId,
      jobApplicationId,
      scheduleAt,
      startTime,
      endTime,
      roundType,
      description,
      fileUrlArray,
      interviewId,
    } = data;
    console.log("data===>>>>", data);
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const jobApplicationRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          JobApplicationsModel
        );

      const jobApplication = await jobApplicationRepo.findOne({
        where: { id: +jobApplicationId },
      });

      if (!jobApplication || jobApplication.status !== Status.APPROVED) {
        return {
          success: false,
          message: API_RESPONSE_MSG.job_application_is_not_approved,
        };
      }

      const scheduledAtDate = new Date(scheduleAt);
      const startTimeDate = new Date(startTime);
      const endTimeDate = new Date(endTime);

      const now = new Date().getTime();
      if (startTimeDate.getTime() < now || endTimeDate.getTime() < now) {
        return {
          success: false,
          message: API_RESPONSE_MSG.cannot_schedule_interview_past,
        };
      }

      // Check if end time is after start time
      if (endTimeDate.getTime() <= startTimeDate.getTime()) {
        return {
          success: false,
          message: API_RESPONSE_MSG.end_time_must_be_after_start_time,
        };
      }

      // Check if interview is scheduled more than one month in advance
      if (startTimeDate.getTime() > now + SCHEDULE_INTERVIEW_ONE_MONTH_MS) {
        return {
          success: false,
          message:
            API_RESPONSE_MSG.cannot_schedule_more_than_one_month_in_advance,
        };
      }

      // Check if there is at least 10 minutes between start and end time
      if (
        endTimeDate.getTime() - startTimeDate.getTime() <
        SCHEDULE_INTERVIEW_MINUTES_MS
      ) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_must_be_at_least_10_min,
        };
      }

      // add max of 2 hours between start and end time
      if (
        endTimeDate.getTime() - startTimeDate.getTime() >
        MAX_HOURS_BETWEEN_START_AND_END_TIME
      ) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_must_not_exceed_2_hours,
        };
      }

      if (interviewId) {
        // Update existing interview
        const interview = await interviewRepo.findOne({
          where: { id: +interviewId },
        });

        if (!interview) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_not_found,
          };
        }

        if (interview.isEnded) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_is_ended,
          };
        }

        const overlappingInterviewsCount = await this.overlappingInterviews(
          interview.jobApplicationId,
          interview.jobId,
          roundType,
          startTimeDate,
          endTimeDate,
          interview.id
        );

        if (overlappingInterviewsCount > 0) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_already_scheduled,
          };
        }

        const previousInterviewerInfo = await AuthServices.getUserByUserId(
          interview.interviewerId
        );

        // sent email only when interviewer is changed
        await this.prepareAndSendInterviewEmail({
          jobId: interview.jobId,
          candidateId: jobApplication.candidateId,
          interviewerId,
          startTimeDate,
          endTimeDate,
          roundType,
          roundNumber: interview.roundNumber,
          fileUrlArray,
          type: INTERVIEW_EMAIL_TYPE.UPDATE,
          previousDate: interview.startTime.toDateString(),
          previousInterviewerName: `${previousInterviewerInfo.first_name} ${previousInterviewerInfo.last_name}`,
          previousInterviewerEmail: previousInterviewerInfo.email,
        });

        // Update interview properties
        interview.title = title;
        interview.interviewerId = +interviewerId;
        interview.roundType = roundType;
        interview.scheduleAt = scheduledAtDate;
        interview.startTime = startTimeDate;
        interview.endTime = endTimeDate;
        interview.description = description;
        interview.attachments = fileUrlArray
          ? { fileUrls: JSON.parse(fileUrlArray) }
          : null;
        interview.updatedTs = new Date();

        const updatedInterview = await interviewRepo.save(interview);
        console.log("updatedInterview====", updatedInterview);
        return {
          success: true,
          data: updatedInterview,
          message: API_RESPONSE_MSG.interview_scheduled_successfully,
        };
      }

      // proceed with schedule interview
      const existingInterviewInfo = await interviewRepo.findOne({
        where: {
          jobId: +jobId,
          jobApplicationId: +jobApplicationId,
        },
        order: {
          id: "DESC",
        },
      });

      console.log("existingInterviewInfo===>>>", existingInterviewInfo);

      if (
        existingInterviewInfo &&
        (!existingInterviewInfo.isEnded ||
          !existingInterviewInfo.isAllowedForNextRound)
      ) {
        return {
          success: false,
          message: API_RESPONSE_MSG.previously_scheduled_interview_is_not_ended,
        };
      }

      console.log("scheduleDateUTC===>>>", scheduledAtDate);
      console.log("startTimeUTC===>>>", startTimeDate);
      console.log("endTimeUTC===>>>", endTimeDate);

      const overlappingInterviewsCount = await this.overlappingInterviews(
        jobApplicationId,
        jobId,
        roundType,
        startTimeDate,
        endTimeDate
      );

      if (overlappingInterviewsCount > 0) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_already_scheduled,
        };
      }

      const roundNumber = existingInterviewInfo?.roundNumber
        ? Number(existingInterviewInfo.roundNumber) + 1
        : 1;

      console.log("roundNumber===>>>", roundNumber);

      const interview = new InterviewModel();
      interview.title = title;
      interview.jobId = +jobId;
      interview.interviewerId = +interviewerId;
      interview.jobApplicationId = +jobApplicationId;
      interview.scheduleAt = scheduledAtDate;
      interview.startTime = startTimeDate;
      interview.endTime = endTimeDate;
      interview.roundType = roundType;
      interview.roundNumber = roundNumber;
      interview.description = description;
      interview.scheduledBy = +userId;
      interview.attachments = fileUrlArray
        ? { fileUrls: JSON.parse(fileUrlArray) }
        : null;

      await interviewRepo.save(interview);

      await this.prepareAndSendInterviewEmail({
        jobId,
        candidateId: jobApplication.candidateId,
        interviewerId,
        startTimeDate,
        endTimeDate,
        roundType,
        roundNumber,
        fileUrlArray,
        type: INTERVIEW_EMAIL_TYPE.SCHEDULE,
      });

      this.generateInterviewSkillQuestions({
        jobId,
        jobApplicationId,
        roundNumber,
        interviewId: interview.id,
      });

      return {
        success: true,
        message: API_RESPONSE_MSG.interview_scheduled_successfully,
      };
    } catch (error) {
      console.log("scheduleInterview error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static getInterviewSkillQuestions = async (
    data: IGetInterviewSkillQuestions
  ) => {
    console.log("data===>>>", data);
    try {
      const { jobApplicationId, interviewId } = data;

      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const interview = await interviewRepo.findOne({
        where: { id: interviewId },
        select: ["id", "hardSkillMarks"],
      });

      const response = await interviewSkillQuestionsAnswersRepo
        .createQueryBuilder("isqa")
        .leftJoinAndSelect("isqa.jobSkill", "jobSkill")
        .leftJoinAndSelect("jobSkill.skill", "skill")
        .leftJoinAndSelect(
          "interview_skill_evaluations",
          "ise",
          "ise.jobSkillId = jobSkill.id AND ise.interviewId = :interviewId",
          { interviewId }
        )
        .where(
          "isqa.jobApplicationId = :jobApplicationId AND isqa.interviewId = :interviewId AND (jobSkill.type = :type OR jobSkill.type IS NULL)",
          { jobApplicationId, interviewId, type: SkillType.CAREER_BASED }
        )
        .orWhere(
          "isqa.jobApplicationId = :jobApplicationId AND jobSkill.type IN (:...types)",
          {
            jobApplicationId,
            interviewId,
            types: [SkillType.ROLE_SPECIFIC, SkillType.CULTURE_SPECIFIC],
          }
        )
        .select([
          "isqa.id as id",
          "isqa.jobSkillId as jobSkillId",
          "isqa.interviewId as interviewId",
          "isqa.jobApplicationId as jobApplicationId",
          "isqa.question as question",
          "isqa.answer as answer",
          "jobSkill.type as questionType",
          "jobSkill.skillId as skillId",
          "skill.title as skillTitle",
          "ise.interviewerId as interviewerId",
          "ise.skillMarks as skillMarks",
          "isqa.createdTs as createdTs",
          "isqa.updatedTs as updatedTs",
        ])
        .getRawMany();

      console.log("response===>>>", response);

      const careerBasedSkillsQuestions = response.filter(
        (skill) => skill.interviewId === +interviewId
      );
      const roleSpecificSkillsQuestions = response.filter(
        (skill) =>
          skill.interviewId !== +interviewId &&
          skill.questionType === SkillType.ROLE_SPECIFIC
      );
      const cultureSpecificSkillsQuestions = response.filter(
        (skill) =>
          skill.interviewId !== +interviewId &&
          skill.questionType === SkillType.CULTURE_SPECIFIC
      );

      const userIds = new Set(response.map((skill) => skill.interviewerId));
      console.log("userIds===>>>", userIds);

      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const users = await userRepo.find({
        where: { id: In([...userIds]) },
        select: ["id", "first_name", "last_name"],
      });
      console.log("users===>>>", users);

      const userMap = new Map();
      users.forEach((user) => {
        userMap.set(
          user.id,
          `${user.first_name.charAt(0).toUpperCase()}${user.last_name.charAt(0).toUpperCase()}`
        );
      });
      console.log("userMap===>>>", userMap);

      // Group role specific questions by skill title
      const formattedRoleSpecificSkillsQuestionsGroupedBySkill =
        roleSpecificSkillsQuestions.reduce((acc, item) => {
          if (!acc[item.skillTitle]) {
            acc[item.skillTitle] = {
              questions: [],
              score: item.skillMarks,
            };
          }
          acc[item.skillTitle].interviewerName =
            userMap.get(item.interviewerId) || "";
          acc[item.skillTitle].questions.push(item);
          return acc;
        }, {});

      // Group culture specific questions by skill title
      const formattedCultureSpecificSkillsQuestionsGroupedBySkill =
        cultureSpecificSkillsQuestions.reduce((acc, item) => {
          if (!acc[item.skillTitle]) {
            acc[item.skillTitle] = {
              questions: [],
              score: item.skillMarks,
            };
          }
          acc[item.skillTitle].interviewerName =
            userMap.get(item.interviewerId) || "";
          acc[item.skillTitle].questions.push(item);
          return acc;
        }, {});

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: {
          roleSpecificQuestions:
            formattedRoleSpecificSkillsQuestionsGroupedBySkill,
          cultureSpecificQuestions:
            formattedCultureSpecificSkillsQuestionsGroupedBySkill,
          careerBasedQuestions: {
            questions: careerBasedSkillsQuestions,
            score: interview?.hardSkillMarks || 0,
          },
        },
      };
    } catch (error) {
      console.log("getInterviewSkillQuestionsAnswers error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static updateInterviewSkillQuestion = async (
    data: IUpdateInterviewSkillQuestion
  ) => {
    console.log("data===>>>", data);
    try {
      const { interviewQuestionId, question } = data;

      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );

      const interviewSkillQuestion =
        await interviewSkillQuestionsAnswersRepo.findOne({
          where: { id: interviewQuestionId },
        });

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const interview = await interviewRepo.findOne({
        where: { id: interviewSkillQuestion.interviewId },
      });

      if (interview.isEnded) {
        return {
          success: false,
          message: API_RESPONSE_MSG.cannot_update_interview_after_ended,
        };
      }

      if (!interviewSkillQuestion) {
        return {
          success: false,
          message: API_RESPONSE_MSG.question_not_found,
        };
      }

      interviewSkillQuestion.question = question;
      interviewSkillQuestion.updatedTs = new Date();

      await interviewSkillQuestionsAnswersRepo.save(interviewSkillQuestion);

      return {
        success: true,
        message: API_RESPONSE_MSG.interview_question_updated,
      };
    } catch (error) {
      console.log("updateInterviewSkillQuestion error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static addInterviewSkillQuestion = async (
    data: IAddInterviewSkillQuestion
  ) => {
    try {
      const { jobApplicationId, interviewId, question, skillType, jobSkillId } =
        data;

      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const interview = await interviewRepo.findOne({
        where: { id: interviewId },
      });

      if (interview.isEnded) {
        return {
          success: false,
          message:
            API_RESPONSE_MSG.cannot_add_interview_skill_question_after_ended,
        };
      }

      // Check if maximum questions limit is reached based on skill type
      if (skillType === SkillType.CAREER_BASED) {
        // Count existing career based questions for this interview
        const careerBasedCount = await interviewSkillQuestionsAnswersRepo.count(
          {
            where: {
              interviewId,
              jobApplicationId,
            },
          }
        );

        console.log("careerBasedCount===>>>", careerBasedCount);

        if (careerBasedCount >= 8) {
          return {
            success: false,
            message: API_RESPONSE_MSG.max_career_based_questions_reached,
          };
        }
      } else {
        // For role or culture specific questions, validate jobSkillId
        if (!jobSkillId) {
          return {
            success: false,
            message: API_RESPONSE_MSG.invalid_data,
          };
        }

        // Count existing questions for this specific job skill
        const skillSpecificCount =
          await interviewSkillQuestionsAnswersRepo.count({
            where: {
              jobSkillId,
              jobApplicationId,
            },
          });

        console.log("skillSpecificCount===>>>", skillSpecificCount);

        // Check limit based on skill type
        if (
          (skillType === SkillType.ROLE_SPECIFIC ||
            skillType === SkillType.CULTURE_SPECIFIC) &&
          skillSpecificCount >= 6
        ) {
          return {
            success: false,
            message:
              skillType === SkillType.ROLE_SPECIFIC
                ? API_RESPONSE_MSG.max_role_specific_questions_reached
                : API_RESPONSE_MSG.max_culture_specific_questions_reached,
          };
        }
      }

      const interviewSkillQuestion = new InterviewSkillQuestionsAnswersModel();
      interviewSkillQuestion.jobApplicationId = jobApplicationId;
      interviewSkillQuestion.interviewId =
        skillType === SkillType.CAREER_BASED ? interviewId : null;
      interviewSkillQuestion.jobSkillId =
        skillType === SkillType.CAREER_BASED ? null : jobSkillId;
      interviewSkillQuestion.question = question;
      interviewSkillQuestion.source = SourceType.MANUAL;

      await interviewSkillQuestionsAnswersRepo.save(interviewSkillQuestion);

      return {
        success: true,
        message: API_RESPONSE_MSG.interview_question_added,
      };
    } catch (error) {
      console.log("addInterviewSkillQuestion error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static updateInterviewAnswers = async (
    data: IUpdateInterviewAnswers,
    userId: number
  ) => {
    try {
      const {
        interviewId,
        skillMarked,
        jobSkillId,
        skillId,
        skillType,
        answers,
      } = data;

      console.log("data===>>>", data);
      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interview = await interviewRepo.findOne({
        where: {
          id: interviewId,
        },
      });

      if (interview?.isEnded) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_is_ended,
        };
      }

      if (interview.interviewerId !== userId) {
        return {
          success: false,
          message: API_RESPONSE_MSG.unauthorized,
        };
      }

      if (answers.length > 0) {
        // Create a query builder for bulk updates
        console.log("inside length check");

        // Process all answers in a single transaction
        await interviewSkillQuestionsAnswersRepo.manager.transaction(
          async (transactionalEntityManager) => {
            const updatePromises = answers.map(
              ({ questionId, answer }) =>
                answer?.length &&
                transactionalEntityManager
                  .createQueryBuilder()
                  .update(InterviewSkillQuestionsAnswersModel)
                  .set({
                    answer,
                    updatedTs: new Date(),
                  })
                  .where("id = :questionId", { questionId })
                  .execute()
            );

            // Execute all updates in parallel within the transaction
            await Promise.all(updatePromises);
          }
        );
      }

      if (skillType === SkillType.CAREER_BASED) {
        const res = await interviewRepo.update(interviewId, {
          hardSkillMarks: skillMarked,
          updatedTs: new Date(),
        });
        console.log("update res===>>>", res);
      } else {
        const interviewSkillEvaluationRepo =
          await dbConnection.getS9InnerViewDatabaseRepository(
            InterviewSkillEvaluationModel
          );

        const isEvaluationAlreadyExist =
          await interviewSkillEvaluationRepo.findOne({
            where: {
              interviewId,
              jobSkillId,
            },
          });

        console.log("isEvaluationAlreadyExist===>>>", isEvaluationAlreadyExist);

        if (isEvaluationAlreadyExist?.locked) {
          return {
            success: false,
            message: API_RESPONSE_MSG.interview_already_ended,
          };
        }

        if (isEvaluationAlreadyExist) {
          await interviewSkillEvaluationRepo.update(
            isEvaluationAlreadyExist.id,
            {
              skillMarks: skillMarked,
              updatedTs: new Date(),
            }
          );
        } else {
          const newInterviewSkillEvaluation =
            new InterviewSkillEvaluationModel();
          newInterviewSkillEvaluation.interviewerId = userId;
          newInterviewSkillEvaluation.interviewId = interviewId;
          newInterviewSkillEvaluation.skillMarks = skillMarked;
          newInterviewSkillEvaluation.jobSkillId = jobSkillId;
          newInterviewSkillEvaluation.skillId = skillId;
          await interviewSkillEvaluationRepo.save(newInterviewSkillEvaluation);
        }
      }

      return {
        success: true,
        code: 200,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.error("updateInterviewQuestionAnswers error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        code: 500,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static generateInterviewSkillQuestions = async (
    data: IGenerateInterviewSkillQuestions
  ) => {
    console.log("data===>>>", data);
    try {
      const { jobId, jobApplicationId, roundNumber, interviewId } = data;
      const interviewSkillQuestionsAnswersRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillQuestionsAnswersModel
        );

      const jobSkillsRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(JobSkillsModel);

      // First check if role_specific and culture_specific questions already exist for this job application
      const existingQuestionsCount = await interviewSkillQuestionsAnswersRepo
        .createQueryBuilder("qa")
        .innerJoin("qa.jobSkill", "js")
        .where("qa.jobApplicationId = :jobApplicationId", {
          jobApplicationId,
        })
        .andWhere("js.type IN (:...types)", {
          types: [SkillType.ROLE_SPECIFIC, SkillType.CULTURE_SPECIFIC],
        })
        .getCount();

      console.log("existingQuestionsCount===>>>", existingQuestionsCount);

      // Get job skills by types
      const getSkillsByType = async (type?: string) => {
        const queryBuilder = jobSkillsRepo
          .createQueryBuilder("jobSkill")
          .leftJoinAndSelect("jobSkill.skill", "skill")
          .where("jobSkill.jobId = :jobId", { jobId });
        if (type) {
          queryBuilder.andWhere("jobSkill.type = :type", { type });
        }
        queryBuilder.select([
          "jobSkill.id",
          "jobSkill.type",
          "skill.title",
          "skill.shortDescription",
        ]);
        return queryBuilder.getRawMany();
      };

      // Create questions for a specific skill type
      const createQuestionsForSkillType = async (
        jobSkillType?: string,
        difficultyLevel = INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS.BEGINNER
      ) => {
        const skillsInfo = await getSkillsByType(jobSkillType);

        const result = await this.generateQuestionForSkill(
          skillsInfo,
          difficultyLevel
        );

        const skills = JSON.parse(result);

        console.log(
          typeof skills,
          "skills===>>>",
          JSON.stringify(skills, null, 2)
        );

        if (skills && skills.questions) {
          const questionsToInsert = [];

          for (const jobSkillId in skills.questions) {
            const questionList = skills.questions[jobSkillId];
            questionList.forEach((questionObj) => {
              questionsToInsert.push({
                jobSkillId: Number(jobSkillId),
                interviewId:
                  questionObj.skillType === SkillType.CAREER_BASED
                    ? interviewId
                    : null,
                jobApplicationId,
                question: questionObj.questionText,
                answer: "",
                source: SourceType.AI,
              });
            });
          }

          if (questionsToInsert.length) {
            await interviewSkillQuestionsAnswersRepo.save(questionsToInsert);
          }
        }
      };

      if (roundNumber > 1) {
        // create career based questions
        await createQuestionsForSkillType(
          SkillType.CAREER_BASED,
          roundNumber > 3
            ? INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS.ADVANCED
            : INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS.INTERMEDIATE
        );
      } else if (roundNumber === 1) {
        await createQuestionsForSkillType(
          existingQuestionsCount ? SkillType.CAREER_BASED : undefined
        );
      }

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.log("generateInterviewSkillQuestionsAnswers error===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static generateQuestionForSkill = async (
    jobSkill: {
      id: number;
      type: string;
      title: string;
      shortDescription: string;
    }[],
    difficultyLevel: string
  ) => {
    const openAiClient = new OpenAI({ apiKey: OPENAI_API_KEY });

    const response = await openAiClient.chat.completions.create({
      model: GPT_MODEL,
      messages: [
        {
          role: "system",
          content: `You are an expert HR interviewer and question generator. Generate professional, insightful interview questions that assess candidates effectively.
          `,
        },
        {
          role: "user",
          content: `
            Generate exactly 3 interview questions for each skill (role and culture specific) listed below and for career based only 1 question for each skill.
            character limit for question is 500.

            **Skills Data**:
            ${JSON.stringify(jobSkill)}

            jobSkill fomat is:
            {
              jobSkill_id: number,
              jobSkill_type: "career_based" | "role_specific" | "culture_specific",
              skill_title: string,
              skill_short_description: string
            }

            **Requirements**:
            - Generate exactly 3 questions per skill
            - Professional and clear language
            - 15-25 words per question
            - Mix of behavioral, situational, and direct assessment questions
            - Questions should assess the specific skill described
            - difficultyLevel: ${difficultyLevel}

            **Response Format**:
            json
            {
              "questions": {
                  jobSkillId: [
                    {
                      questionText: string,
                      difficultyLevel: ${difficultyLevel}
                      skillType: string
                    }
                  ]
                  // This is an array of 3 questions
              }
            }
          `,
        },
      ],
    });

    const responseContent = response.choices[0].message.content || "";

    // Extract JSON from the response by removing markdown formatting
    let jsonContent = responseContent;

    // Remove markdown code block indicators if present
    if (responseContent.includes("```json")) {
      jsonContent = responseContent.replace(/```json\n|\n```/g, "");
    } else if (responseContent.includes("```")) {
      jsonContent = responseContent.replace(/```\n|\n```/g, "");
    }

    return jsonContent;
  };

  static getUpcomingOrPastInterviews = async (
    orgId: number,
    searchStr?: string, // ✅ Added: Enables filtering by candidate name or job title
    isPast: boolean = false,
    offset: number = DEFAULT_OFFSET, // ✅ Added: Enables pagination (skips 'offset' records)
    limit: number = DEFAULT_LIMIT // ✅ Added: Limits the number of results returned
    // ✅ Added: Enables filtering by candidate name or job title
  ) => {
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const now = new Date();

      const query = interviewRepo
        .createQueryBuilder("interview")
        .leftJoin(
          JobApplicationsModel,
          "jobApplication",
          "interview.jobApplicationId = jobApplication.id"
        )
        .leftJoin(
          CandidatesModel,
          "candidate",
          "jobApplication.candidateId = candidate.id"
        )
        .leftJoin(JobsModel, "job", "interview.jobId = job.id")
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere(
          isPast
            ? new Brackets((qb) => {
                qb.where("interview.startTime < :now", { now }).orWhere(
                  "interview.isEnded = :isEnded",
                  { isEnded: true }
                );
              })
            : new Brackets((qb) => {
                qb.where("interview.startTime >= :now", { now }).andWhere(
                  "interview.isEnded = :isEnded",
                  { isEnded: false }
                );
              })
        )
        .select([
          "interview.id AS interviewId",
          "interview.jobApplicationId AS jobApplicationId",
          "interview.startTime AS startTime",
          "interview.endTime AS endTime",
          "interview.isEnded AS isEnded",
          "interview.roundType AS roundType",
          "jobApplication.resumeFile AS resumeFile",
          "candidate.name AS candidateName",
          "job.title AS jobTitle",
        ]);

      // 🔍 Filtering logic using search string
      // ✅ Added: This allows filtering by candidate name or job title using a case-insensitive search
      if (searchStr?.trim()) {
        const likeStr = `%${searchStr.trim().toLowerCase()}%`;
        query.andWhere(
          "(LOWER(candidate.name) LIKE :searchStr OR LOWER(job.title) LIKE :searchStr)",
          { searchStr: likeStr }
        );
      }
      query.orderBy("interview.startTime", isPast ? "DESC" : "ASC");

      query.offset(offset).limit(limit);

      const interviews = await query.getRawMany();

      return {
        success: true,
        code: 200,
        message: `Fetched ${isPast ? "past" : "upcoming"} interviews successfully`,
        data: interviews,
      };
    } catch (error) {
      console.error("getUpcomingOrPastInterviews error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        code: 500,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static endInterview = async ({
    interviewId,
    behaviouralNotes: behavioralNotes,
  }: IEndInterview) => {
    try {
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);
      const evaluationRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          InterviewSkillEvaluationModel
        );

      const interview = await interviewRepo.findOne({
        where: { id: interviewId },
      });

      if (interview?.isEnded) {
        return {
          success: false,
          message: API_RESPONSE_MSG.interview_already_ended,
        };
      }

      // Update the interview
      await interviewRepo.update(
        { id: interviewId },
        {
          isEnded: true,
          applicantBehavioralNotes: behavioralNotes,
        }
      );

      // Lock all evaluation rows for this interview
      await evaluationRepo.update({ interviewId }, { locked: true });

      return {
        success: true,
        code: 200,
        message: API_RESPONSE_MSG.interview_ended,
      };
    } catch (error) {
      console.error("endInterview error ===>>>", error);
      Sentry.captureException(error);

      return {
        success: false,
        code: 500,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };
}

export default InterviewServices;
