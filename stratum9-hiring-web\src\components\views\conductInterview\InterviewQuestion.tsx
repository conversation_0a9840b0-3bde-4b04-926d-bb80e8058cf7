/* eslint-disable @typescript-eslint/no-explicit-any */
import { use, useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { io, Socket } from "socket.io-client";
// import Link from "next/link";
import { useDispatch, useSelector } from "react-redux";

import Button from "@/components/formElements/Button";
import BackArrowIcon from "@/components/svgComponents/BackArrowIcon";
import PreviewResumeIcon from "@/components/svgComponents/PreviewResumeIcon";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textarea from "@/components/formElements/Textarea";
import ArrowDownIcon from "@/components/svgComponents/ArrowDownIcon";
// import RightGreenIcon from "@/components/svgComponents/RightGreenIcon";
// import WrongRedIcon from "@/components/svgComponents/WrongRedIcon";
import ProgressTracker from "./ProgressTracker";
import RecIcon from "@/components/svgComponents/RecIcon";
import LetterFoldIcon from "@/components/svgComponents/LetterFoldIcon";
import style from "../../../styles/conductInterview.module.scss";
import { IGetInterviewSkillQuestionsResponse, IInterviewQuestionResponse, IInterviewQuestionFormValues } from "@/interfaces/interviewInterfaces";
import { QUESTION_TYPES } from "@/constants/commonConstants";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { endInterview, updateInterviewAnswers } from "@/services/interviewServices";
import { clearInterview, updateQuestionAnswer } from "@/redux/slices/interviewSlice";
import EndInterViewModal from "@/components/commonModals/EndInterViewModal";
import { getSession } from "next-auth/react";
import { ISession } from "@/interfaces/commonInterfaces";
import { yupResolver } from "@hookform/resolvers/yup";
import { addAnswerValidation } from "@/validations/interviewValidations";
import { AuthState } from "@/redux/slices/authSlice";
import ROUTES from "@/constants/routes";

const InterviewQuestion = ({ params }: { params: Promise<{ interviewId: string; jobApplicationId: string; resumeLink: string }> }) => {
  const searchParamsPromise = use(params);
  const interviewId = +searchParamsPromise.interviewId;
  const resumeLink = searchParamsPromise.resumeLink;

  const interviewQuestionsData = useSelector((state: { interview: IGetInterviewSkillQuestionsResponse }) => state.interview);
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);

  const router = useRouter();
  const t = useTranslations();
  const dispatch = useDispatch();
  const socketRef = useRef<Socket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const [isRecording, setIsRecording] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    getValues,
    watch,
    formState: { errors },
  } = useForm<IInterviewQuestionFormValues | any>({ mode: "onChange", resolver: yupResolver(addAnswerValidation(t)) });
  const [openQuestions, setOpenQuestions] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [loader, setLoader] = useState(false);
  const [showEndInterviewModal, setShowEndInterviewModal] = useState(false);

  const [selectedTab, setSelectedTab] = useState<string>(QUESTION_TYPES.CAREER_BASED);
  const [selectedSkill, setSelectedSkill] = useState("");
  const [skillMarked, setSkillMarked] = useState(0);

  const [currentCultureSkillIndex, setCurrentCultureSkillIndex] = useState(0);
  const [currentRoleSkillIndex, setCurrentRoleSkillIndex] = useState(0);

  const roleSkills = useMemo(() => Object.keys(interviewQuestionsData.roleSpecificQuestions || {}), [interviewQuestionsData.roleSpecificQuestions]);

  const cultureSkills = useMemo(
    () => Object.keys(interviewQuestionsData.cultureSpecificQuestions || {}),
    [interviewQuestionsData.cultureSpecificQuestions]
  );

  console.log("===========errors", control._formState.errors);

  console.log("====selectedSkill", selectedSkill);

  useEffect(() => {
    prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);
    setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);
  }, []);

  // Function to initialize socket with the token
  const initializeSocket = (token: string) => {
    if (socketRef.current) {
      socketRef.current.disconnect(); // Disconnect the previous socket connection
    }

    // Initialize the socket with the new token
    socketRef.current = io("http://localhost:3001", {
      path: "/conduct-interview",
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      extraHeaders: {
        Authorization: `Bearer ${token}`,
      },
      query: {
        interviewId: interviewId.toString(),
      },
    });

    // Add socket event listeners
    socketRef.current?.on("connect", () => {
      console.log("Connected to server");
    });

    socketRef.current?.on("disconnect", (reason, details) => {
      console.log("Disconnected from server:", reason, details);
    });

    socketRef.current?.on("connect_error", (error) => {
      console.log("Connect error:", error);
    });
  };

  const startSocketConnection = async () => {
    const session = await getSession();
    const parsedSession = { ...session } as ISession;
    const token = parsedSession?.user?.data?.token;
    console.log("token", token);
    if (token) {
      initializeSocket(token);
    }
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect();
      }
    };
  };

  const handleOpen = (questionId: number) => {
    setOpenQuestions((prev) => {
      // If the question is already open, close it
      if (prev.includes(questionId)) {
        return prev.filter((id) => id !== questionId);
      }
      // Otherwise, add it to the open questions array
      return [...prev, questionId];
    });
  };

  async function openMicrophone(microphone: MediaRecorder, socket: Socket | null) {
    console.log("microphone======", microphone);
    return new Promise<void>((resolve) => {
      let buffer: Blob[] = [];
      const bufferInterval = 1000;
      microphone.onstart = () => {
        resolve();
      };

      microphone.onstop = () => {
        if (buffer.length > 0 && socket?.connected) {
          const finalBlob = new Blob(buffer, { type: "audio/webm" });
          socket.emit("message", finalBlob);
          buffer = [];
        }
      };

      microphone.ondataavailable = (event: BlobEvent) => {
        console.log("event======", event);
        if (event.data.size > 0) {
          buffer.push(event.data);
        }
      };

      const sendInterval = setInterval(() => {
        if (buffer.length > 0 && socket?.connected) {
          const audioBlob = new Blob(buffer, { type: "audio/webm" });
          socket.emit("message", audioBlob);
          buffer = [];
        }
      }, bufferInterval);

      microphone.start(500);

      console.log("buffer======", buffer);

      const cleanup = () => {
        clearInterval(sendInterval);
        if (microphone.state !== "inactive") {
          microphone.stop();
        }
      };
      socket?.on("disconnect", cleanup);
      socket?.on("error", cleanup);
    });
  }

  const startTranscription = useCallback(async () => {
    try {
      setIsRecording(true);
      await startSocketConnection();
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log("stream======", stream);
      streamRef.current = stream;
      const options = { mimeType: "audio/webm" };
      const recorder = new MediaRecorder(stream, options);
      console.log("recorder======", recorder);
      mediaRecorderRef.current = recorder;
      await openMicrophone(recorder, socketRef.current);
    } catch (error) {
      console.error("Error starting transcription:", error);
    }
  }, []);

  const stopTranscription = useCallback(async () => {
    setIsRecording(false);
    if (mediaRecorderRef.current) {
      if (mediaRecorderRef.current.state !== "inactive") {
        mediaRecorderRef.current.stop();
      }
      mediaRecorderRef.current = null;
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track: MediaStreamTrack) => track.stop());
      streamRef.current = null;
    }

    // so that previous audio is processed
    setTimeout(() => {
      if (socketRef.current) {
        socketRef.current.close();
        socketRef.current = null;
      }
    }, 5000);
  }, []);

  console.log(control._formValues, "control._formValues===========");

  const handleSaveAndNext = async (data: IInterviewQuestionFormValues) => {
    try {
      console.log("inside handleSaveAndNext");
      if (skillMarked === 0) {
        toastMessageError(t("please_mark_stratum_score"));
        return;
      }
      setLoading(true);
      const { behavioralInfo, ...rest } = data;

      console.log("==========behavioralInfo", behavioralInfo);

      // Transform the data into the required format
      const answers = Object.entries(rest).map(([key, value]) => {
        // Extract the ID from the key (e.g., "answer--->>148" -> 148)
        const questionId = parseInt(key.replace("answer-", ""));

        return {
          questionId,
          answer: value,
        };
      });

      let skillId;
      let jobSkillId;

      if (selectedTab === QUESTION_TYPES.ROLE_SPECIFIC) {
        skillId = interviewQuestionsData.roleSpecificQuestions[selectedSkill].questions[0].skillId;
        jobSkillId = interviewQuestionsData.roleSpecificQuestions[selectedSkill].questions[0].jobSkillId;
      } else if (selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC) {
        skillId = interviewQuestionsData.cultureSpecificQuestions[selectedSkill].questions[0].skillId;
        jobSkillId = interviewQuestionsData.cultureSpecificQuestions[selectedSkill].questions[0].jobSkillId;
      }

      const payload = {
        interviewId: interviewId,
        skillId,
        jobSkillId,
        skillMarked: skillMarked,
        skillType: selectedTab,
        answers,
      };

      console.log("==========payload", payload);

      // After saving, handle navigation based on current section and skill
      const response = await updateInterviewAnswers(payload);

      const interviewerName = `${authData?.first_name.charAt(0).toUpperCase()}${authData?.last_name.charAt(0).toUpperCase()}`;
      console.log("interviewerName", interviewerName);

      console.log("api response====", response);

      if (response?.data?.success) {
        console.log("interviewerName", interviewerName);
        switch (selectedTab) {
          case QUESTION_TYPES.CAREER_BASED:
            dispatch(
              updateQuestionAnswer({
                questionType: selectedTab,
                questionAnswers: answers,
                stratumScore: skillMarked,
              })
            );
            break;
          case QUESTION_TYPES.ROLE_SPECIFIC:
            dispatch(
              updateQuestionAnswer({
                questionType: selectedTab,
                category: selectedSkill,
                questionAnswers: answers,
                stratumScore: skillMarked,
                interviewerName,
              })
            );
            break;
          case QUESTION_TYPES.CULTURE_SPECIFIC:
            dispatch(
              updateQuestionAnswer({
                questionType: selectedTab,
                category: selectedSkill,
                questionAnswers: answers,
                stratumScore: skillMarked,
                interviewerName,
              })
            );
            break;
        }
      }

      setTimeout(() => {
        handleNextSkillInterview();
      }, 100);
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoading(false);
    }
  };

  const handleEndInterview = async () => {
    try {
      setLoader(true);
      const response = await endInterview({ interviewId, behaviouralNotes: getValues("behavioralInfo") ?? "" });

      if (response?.data?.success) {
        toastMessageSuccess(t(response?.data?.message));
        dispatch(clearInterview());
        router.push(ROUTES.DASHBOARD);
      } else {
        toastMessageError(t(response?.data?.message));
      }
    } catch {
      toastMessageError(t("something_went_wrong"));
    } finally {
      setLoader(false);
    }
  };

  const handlePreviousSkillInterview = () => {
    setOpenQuestions([]);
    const behavioralInfo = getValues("behavioralInfo");
    reset({
      behavioralInfo: behavioralInfo,
    });

    switch (selectedTab) {
      case QUESTION_TYPES.ROLE_SPECIFIC:
        // Check if we're on the first role skill
        if (currentRoleSkillIndex === 0) {
          // Move from Role-specific back to Career-based
          setSelectedTab(QUESTION_TYPES.CAREER_BASED);
          prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);
          setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);
        } else {
          // Move to previous role skill
          const prevIndex = currentRoleSkillIndex - 1;
          setCurrentRoleSkillIndex(prevIndex);
          setSelectedSkill(roleSkills[prevIndex]);
          prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[prevIndex]].questions || []);
          setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[prevIndex]].score);
        }
        break;
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        // Check if we're on the first culture skill
        if (currentCultureSkillIndex === 0) {
          // Move from Culture-specific to Role-specific
          setSelectedTab(QUESTION_TYPES.ROLE_SPECIFIC);
          if (roleSkills.length > 0) {
            setSelectedSkill(roleSkills[roleSkills.length - 1]);
            setCurrentRoleSkillIndex(roleSkills.length - 1);
            prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[roleSkills.length - 1]].questions || []);
            setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[roleSkills.length - 1]].score);
          }
        } else {
          // Move to previous culture skill
          const prevIndex = currentCultureSkillIndex - 1;
          setCurrentCultureSkillIndex(prevIndex);
          setSelectedSkill(cultureSkills[prevIndex]);
          prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[prevIndex]].questions || []);
          setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[prevIndex]].score);
        }
        break;
      case QUESTION_TYPES.CAREER_BASED:
        break;
    }
  };

  const handleNextSkillInterview = () => {
    setOpenQuestions([]);
    const behavioralInfo = getValues("behavioralInfo");
    reset({
      behavioralInfo: behavioralInfo,
    });

    switch (selectedTab) {
      case QUESTION_TYPES.CAREER_BASED:
        // Move from Career-based to Role-specific
        setSelectedTab(QUESTION_TYPES.ROLE_SPECIFIC);
        if (roleSkills.length > 0) {
          setSelectedSkill(roleSkills[0]);
          prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[0]].questions || []);
          setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[0]].score);
        }
        break;
      case QUESTION_TYPES.ROLE_SPECIFIC:
        // Check if there are more role skills to navigate to
        if (currentRoleSkillIndex < roleSkills.length - 1) {
          // Move to next role skill
          const nextIndex = currentRoleSkillIndex + 1;
          setCurrentRoleSkillIndex(nextIndex);
          setSelectedSkill(roleSkills[nextIndex]);
          prefillFormData(interviewQuestionsData.roleSpecificQuestions[roleSkills[nextIndex]].questions || []);
          setSkillMarked(interviewQuestionsData.roleSpecificQuestions[roleSkills[nextIndex]].score);
        } else {
          // Move from Role-specific to Culture-specific
          setSelectedTab(QUESTION_TYPES.CULTURE_SPECIFIC);
          setCurrentRoleSkillIndex(0);
          if (cultureSkills.length > 0) {
            setSelectedSkill(cultureSkills[0]);
            setCurrentCultureSkillIndex(0);
            prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[0]].questions || []);
            setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[0]].score);
          }
        }
        break;
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        // Check if there are more culture skills to navigate to
        if (currentCultureSkillIndex < cultureSkills.length - 1) {
          // Move to next culture skill
          const nextIndex = currentCultureSkillIndex + 1;
          setCurrentCultureSkillIndex(nextIndex);
          setSelectedSkill(cultureSkills[nextIndex]);
          prefillFormData(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[nextIndex]].questions || []);
          setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[cultureSkills[nextIndex]].score);
        }
        break;
    }
  };

  const isLastSkillInSection = (): boolean => {
    switch (selectedTab) {
      case QUESTION_TYPES.CAREER_BASED:
        return true; // Career-based doesn't have sub-skills
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        return currentCultureSkillIndex >= cultureSkills.length - 1;
      case QUESTION_TYPES.ROLE_SPECIFIC:
        return currentRoleSkillIndex >= roleSkills.length - 1;
      default:
        return false;
    }
  };

  const isLastSkillOverall = (): boolean => {
    return selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC && isLastSkillInSection();
  };

  const isSkillCompleted = (skillType: string, skillName: string): boolean => {
    switch (skillType) {
      case QUESTION_TYPES.CAREER_BASED:
        return (
          interviewQuestionsData.careerBasedQuestions?.questions?.some((question) => question.answer !== undefined && question.answer !== "") ||
          !!interviewQuestionsData.careerBasedQuestions.score
        );
      case QUESTION_TYPES.ROLE_SPECIFIC:
        return (
          interviewQuestionsData.roleSpecificQuestions?.[skillName]?.questions?.some(
            (question) => question.answer !== undefined && question.answer !== ""
          ) || !!interviewQuestionsData.roleSpecificQuestions[skillName].score
        );
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        return (
          interviewQuestionsData.cultureSpecificQuestions?.[skillName]?.questions?.some(
            (question) => question.answer !== undefined && question.answer !== ""
          ) || !!interviewQuestionsData.cultureSpecificQuestions[skillName].score
        );
      default:
        return false;
    }
  };

  const prefillFormData = (questions: IInterviewQuestionResponse[]) => {
    if (!questions || questions.length === 0) {
      return;
    }

    // For each question, prefill the form if we have data
    questions.forEach((question) => {
      if (question.answer) {
        // Set the form value for this question
        const fieldName = `answer-${question.id}`;
        // Use reset to set the value while preserving other form values
        reset({
          ...control._formValues,
          [fieldName]: question.answer,
        });

        // Open the question card
        if (!openQuestions.includes(question.id)) {
          setOpenQuestions((prev) => [...prev, question.id]);
        }
      }
    });
  };

  const handleSkillSelection = (skill: string) => {
    setSelectedSkill(skill);

    // update additionalInfo field
    reset({
      ...control._formValues,
      behavioralInfo: getValues("behavioralInfo"),
    });

    // Update the corresponding index tracker based on the selected tab
    switch (selectedTab) {
      case QUESTION_TYPES.ROLE_SPECIFIC:
        const roleIndex = roleSkills.findIndex((s) => s === skill);
        if (roleIndex !== -1) {
          setCurrentRoleSkillIndex(roleIndex);
        }
        // Prefill form data for role-specific skills
        prefillFormData(interviewQuestionsData.roleSpecificQuestions[skill].questions || []);
        setSkillMarked(interviewQuestionsData.roleSpecificQuestions[skill].score);
        break;

      case QUESTION_TYPES.CULTURE_SPECIFIC:
        const cultureIndex = cultureSkills.findIndex((s) => s === skill);
        if (cultureIndex !== -1) {
          setCurrentCultureSkillIndex(cultureIndex);
        }
        // Prefill form data for culture-specific skills
        prefillFormData(interviewQuestionsData.cultureSpecificQuestions[skill].questions || []);
        setSkillMarked(interviewQuestionsData.cultureSpecificQuestions[skill].score);
        break;

      case QUESTION_TYPES.CAREER_BASED:
        // Prefill form data for career-based skills
        prefillFormData(interviewQuestionsData.careerBasedQuestions.questions || []);
        setSkillMarked(interviewQuestionsData.careerBasedQuestions.score);
        break;

      default:
        break;
    }
  };

  const renderQuestion = (question: IInterviewQuestionResponse, index: number) => (
    <div key={question.id} className="interview-question-card ">
      <div onClick={() => handleOpen(question.id)}>
        <p className="tittle">
          {t("question")} {index < 9 ? `0${index + 1}` : index + 1} <ArrowDownIcon className={openQuestions.includes(question.id) ? "rotate" : ""} />
        </p>
        <h5>{question.question}</h5>
      </div>
      {openQuestions.includes(question.id) ? (
        <div className="question-body ">
          <InputWrapper>
            <InputWrapper.Label htmlFor={`answer-${question.id}`} required>
              {t("your_notes")}
            </InputWrapper.Label>
            <Textarea rows={3} name={`answer-${question.id}`} control={control} placeholder={t("additional_info_desc")} className="form-control" />
            {watch(`answer-${question.id}`)?.length > 2000 ? <InputWrapper.Error message={t("answer_max_2000_chars")} /> : null}
          </InputWrapper>
          {/* <div className="follow-up-container">
                <p className="follow-up-text">
                  <span>Follow Up:</span> What tools or systems did you use to stay organized?{" "}
                </p>
                <div className="follow-up-btn">
                  <Button className="clear-btn p-0 m-0">
                    <RightGreenIcon />
                  </Button>
                  <Button className="clear-btn p-0 m-0">
                    <WrongRedIcon />
                  </Button>
                </div>
            </div> 
          */}
        </div>
      ) : null}
    </div>
  );

  const renderAllQuestions = (type: string) => {
    switch (type) {
      case QUESTION_TYPES.CAREER_BASED:
        return interviewQuestionsData.careerBasedQuestions?.questions?.map((question, index) => renderQuestion(question, index)) || [];
      case QUESTION_TYPES.ROLE_SPECIFIC:
        return (
          interviewQuestionsData.roleSpecificQuestions?.[selectedSkill]?.questions?.map((question, index) => renderQuestion(question, index)) || []
        );
      case QUESTION_TYPES.CULTURE_SPECIFIC:
        return (
          interviewQuestionsData.cultureSpecificQuestions?.[selectedSkill]?.questions?.map((question, index) => renderQuestion(question, index)) || []
        );
      default:
        return [];
    }
  };

  const getSkills = (type: string) => (type === QUESTION_TYPES.CULTURE_SPECIFIC ? cultureSkills : roleSkills);

  return (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <Button
            className="danger-outline-btn rounded-md py-3 px-4 mb-5"
            onClick={() => {
              if (isRecording) {
                stopTranscription();
              } else {
                startTranscription();
              }
            }}
          >
            <RecIcon className="me-3" />
            {isRecording ? t("stop_recording") : t("record_interview")}
          </Button>

          <ProgressTracker isRecording={isRecording} />

          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                <Button className="clear-btn p-0 m-0" disabled={selectedTab === QUESTION_TYPES.CAREER_BASED}>
                  <BackArrowIcon onClick={() => handlePreviousSkillInterview()} />
                </Button>{" "}
                {selectedTab === QUESTION_TYPES.CAREER_BASED ? (
                  <>
                    {t("career_based_skills_and_general_interview")} <span>{t("questions")}</span>
                  </>
                ) : (
                  <>
                    {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC ? t("role_specific_interview") : t("culture_specific_interview")}{" "}
                    <span>{t("questions")}</span>
                  </>
                )}
              </h2>
              <Button className="clear-btn text-btn primary p-0 m-0" onClick={() => window.open(resumeLink, "_blank")}>
                <PreviewResumeIcon className="me-2" />
                {t("preview_candidate_resume")}
              </Button>
            </div>
          </div>
        </div>
        <form onSubmit={handleSubmit((data) => handleSaveAndNext(data as IInterviewQuestionFormValues))}>
          <div className="inner-section">
            {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC || selectedTab === QUESTION_TYPES.CULTURE_SPECIFIC ? (
              <>
                <div className={style.question_info_box}>
                  <ul>
                    <li>
                      <span className={style.current} />
                      {t("current")}
                    </li>
                    <li>
                      <span className={style.completed} />
                      {t("completed")}
                    </li>
                    {/* <li>
                      <span className={style.additional} />
                      {t("additional")}
                    </li> */}
                  </ul>
                </div>
                <ul className="interview-topic-list" aria-label="Job roles list">
                  {getSkills(selectedTab)?.map((skill, index) => (
                    <li
                      key={skill}
                      className={`topic-item ${skill === selectedSkill ? "current" : isSkillCompleted(selectedTab, skill) ? "completed" : ""}`}
                      tabIndex={index}
                      onClick={() => handleSkillSelection(skill)}
                    >
                      {skill}
                      {isSkillCompleted(selectedTab, skill) ? (
                        <span className="interviewer-name">
                          {selectedTab === QUESTION_TYPES.ROLE_SPECIFIC
                            ? interviewQuestionsData.roleSpecificQuestions[skill]?.interviewerName
                            : interviewQuestionsData.cultureSpecificQuestions[skill]?.interviewerName}
                        </span>
                      ) : null}
                    </li>
                  ))}
                </ul>
              </>
            ) : null}
            <div className="row">
              <div className="col-md-8">{renderAllQuestions(selectedTab)}</div>
              <div className="col-md-4">
                <div className="behavioral-letter-card">
                  <h5>
                    {t("behavioral")} <span>{t("performance")}</span>
                  </h5>
                  <InputWrapper>
                    <InputWrapper.Label htmlFor="behavioralInfo">{t("describe_candidate_behaviours")}</InputWrapper.Label>
                    <Textarea
                      rows={5}
                      name="behavioralInfo"
                      control={control}
                      placeholder={t("describe_candidate_behaviours")}
                      className="form-control"
                    />
                    <InputWrapper.Error message={errors.behavioralInfo?.message as string} />
                  </InputWrapper>
                  <LetterFoldIcon className="fold-svg" />
                </div>
              </div>
            </div>
            <div className="section-heading">
              <h2>
                {t("score")}{" "}
                <span className="primary">
                  {selectedTab === QUESTION_TYPES.CAREER_BASED ? t("career") : selectedSkill} {t("stratum")}
                </span>
              </h2>
              <p>
                {t("score_the_candidate_for")} {selectedTab === QUESTION_TYPES.CAREER_BASED ? t("career_based") : selectedSkill.toLowerCase()}{" "}
                {t("stratum")} {t("score_range")}
              </p>
            </div>
            <ul className="number-task">
              {Array.from({ length: 10 }).map((_, index) => (
                <li
                  className={index > 8 ? (index + 1 === skillMarked ? "extreme active" : "extreme") : index + 1 === skillMarked ? "active" : ""}
                  key={index}
                  onClick={() => {
                    setSkillMarked(index + 1 === skillMarked ? 0 : index + 1);
                  }}
                >
                  {index > 8 ? t("extreme") : index + 1}
                </li>
              ))}
            </ul>
            <div className="interview-question-card">
              <h5>{t("candidate_not_aware")}</h5>
            </div>
          </div>
          <div className="button-align justify-content-between pt-4 pb-5">
            <div className="button-align ">
              {!isLastSkillOverall() ? (
                <Button className="primary-btn rounded-md" type="button" disabled={loading || loader} onClick={handleNextSkillInterview}>
                  {t("next_skill_interview")}
                </Button>
              ) : null}
              <Button className="dark-outline-btn rounded-md" type="submit" disabled={loading || loader} loading={loading}>
                {t("save_next")}
              </Button>
            </div>
            <Button
              className="danger-btn rounded-md"
              type="button"
              onClick={() => setShowEndInterviewModal(true)}
              loading={loader}
              disabled={loading || loader}
            >
              {t("end_interview")}
            </Button>
          </div>
        </form>
      </div>
      {showEndInterviewModal ? (
        <EndInterViewModal
          onClickCancel={() => setShowEndInterviewModal(false)}
          onClickEndInterview={handleEndInterview}
          disabled={loading || loader}
        />
      ) : null}
    </div>
  );
};

export default InterviewQuestion;
