const ROUTES = {
  LOGIN: "/login",
  FORGOT_PASSWORD: "/forgot-password",
  VERIFY: "/verify",
  RESET_PASSWORD: "/reset-password",
  CANDIDATE_ASSESSMENT: "/candidate-assessment",
  DASHBOARD: "/dashboard",
  HOME: "/",
  PROFILE: {
    MY_PROFILE: "/my-profile",
  },
  JOBS: {
    CAREER_BASED_SKILLS: "/career-based-skills",
    ROLE_BASED_SKILLS: "/role-based-skills",
    CULTURE_BASED_SKILLS: "/culture-based-skills",
    GENERATE_JOB: "/generate-job",
    EDIT_SKILLS: "/edit-skills",
    HIRING_TYPE: "/hiring-type",
    JOB_EDITOR: "/job-editor",
    ACTIVE_JOBS: "/active-jobs",
    CANDIDATE_PROFILE: "/candidate-profile",
    ARCHIVE: "/archive",
  },
  SCREEN_RESUME: {
    MANUAL_CANDIDATE_UPLOAD: "/manual-upload-resume",
    CANDIDATE_QUALIFICATION: "/candidate-qualification",
    CANDIDATE_LIST: "/candidates-list",
    CANDIDATES: "/candidates",
  },
  INTERVIEW: {
    ADD_CANDIDATE_INFO: "/additional-submission",
    SCHEDULE_INTERVIEW: "/schedule-interview",
    PRE_INTERVIEW_QUESTIONS_OVERVIEW: "/pre-interview-questions-overview",
    INTERVIEW_QUESTION: "/interview-question",
    CALENDAR: "/calendar",
    INTERVIEW_SUMMARY: "/interview-summary",
  },

  ROLE_EMPLOYEES: {
    USER_ROLE: "/user-roles",
    EMPLOYEE_MANAGEMENT: "/employee-management",
    EMPLOYEE_MANAGEMENT_DETAIL: "/employee-management-detail",
    ADD_EMPLOYEE: "/add-employees",
    ADD_DEPARTMENT: "/add-department",
  },

  FINAL_ASSESSMENT: {
    FINAL_ASSESSMENT: "/final-assessment",
  },
};

export const BEFORE_LOGIN_ROUTES = [ROUTES.LOGIN, ROUTES.FORGOT_PASSWORD, ROUTES.VERIFY, ROUTES.RESET_PASSWORD, ROUTES.CANDIDATE_ASSESSMENT];

export default ROUTES;
