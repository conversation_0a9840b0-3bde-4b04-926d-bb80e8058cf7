import * as yup from "yup";

export const updateInterviewOrderValidationSchema = (translations: (key: string) => string) => {
  return yup.object().shape({
    interviewOrder: yup
      .number()
      .typeError(translations("interview_order_must_be_number"))
      .required(translations("interview_order_required"))
      .positive(translations("interview_order_positive"))
      .integer(translations("interview_order_integer")),
  });
};

export const profileValidationSchema = (translations: (key: string) => string) => {
  return yup.object().shape({
    firstName: yup.string().trim().required(translations("first_name_required")).min(1, translations("min_name")).max(50, translations("max_name")),
    lastName: yup.string().trim().required(translations("last_name_required")).min(1, translations("min_name")).max(50, translations("max_name")),
  });
};
