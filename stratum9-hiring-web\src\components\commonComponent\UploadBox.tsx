import React from "react";
import UploadDocumentIcon from "../svgComponents/UploadDocumentIcon";
import { useTranslations } from "next-intl";

const UploadBox = ({
  UploadBoxClassName,
  onChange,
  inputRef,
  isLoading,
}: {
  UploadBoxClassName?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  inputRef?: React.RefObject<HTMLInputElement | null>;
  isLoading?: boolean;
}) => {
  const t = useTranslations();
  return (
    <div className={`upload-card ${UploadBoxClassName}`}>
      <input type="file" accept=".pdf" onChange={onChange} disabled={isLoading} ref={inputRef} />
      <div className="upload-box-inner">
        <UploadDocumentIcon />
        {!isLoading ? (
          <p>
            {t("upload_doc")}
            <br />
            {t("max_file_size")}
          </p>
        ) : (
          <p>{t("uploading")}</p>
        )}
      </div>
    </div>
  );
};

export default UploadBox;
