"use client";

import React, { useState } from "react";

// Internal libraries
import Sidebar from "@/components/sidebar/Sidebar";

// External libraries
import Link from "next/link";
import Image from "next/image";

// CSS
import style from "@/styles/commonPage.module.scss";
import user from "../../../../public/assets/images/user.png";
import ApplicationsSourcesModal from "@/components/commonModals/ApplicationsSourcesModal";
import { useTranslations } from "next-intl";

function Candidates() {
  const [showApplicationsSourcesModal, setShowApplicationsSourcesModal] = useState(false);
  const t = useTranslations();
  return (
    <div className="container">
      {/* --- Page Header --- */}
      <div className="common-page-header">
        <div className="breadcrumb">
          <Link href="#"> {t("home")} </Link>
          <Link href="#"> {t("hm_dashboard")} </Link>
          <Link href="#"> {t("candidates")} </Link>
        </div>
        <div className="common-page-head-section">
          <div className="main-heading">
            <h2>
              Hiring Manager Dashboard - <span>{t("candidates")}</span>
            </h2>
            {/* <div className="d-flex">
                <NotificationIcon hasNotification={true} />
              </div> */}
          </div>
        </div>
      </div>

      {/* --- Main Layout --- */}
      <div className="common-box">
        <Sidebar />
        <main className="main-content">
          <div className={style.dashboard_page}>
            {/* --- Hired Candidates Table (No Tabs UI) --- */}
            <div className="table-responsive mt-5">
              <table className="table">
                <thead>
                  <tr>
                    <th>Candidate Name</th>
                    <th>Date Hired</th>
                    <th>Job Compatibility</th>
                    <th>Interviewed By</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        John Doe
                      </Link>
                    </td>
                    <td>Aug 18, 2024</td>
                    <td>80%</td>
                    <td>
                      <ul className="multi-user-list">
                        {[...Array(4)].map((_, i) => (
                          <li key={i}>
                            <Image src={user} alt="user" />
                          </li>
                        ))}
                      </ul>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <Link href="/" className="primary underline">
                        Michael Johnson
                      </Link>
                    </td>
                    <td>Administrator</td>
                    <td>40%</td>
                    <td>
                      <ul className="multi-user-list">
                        {[...Array(4)].map((_, i) => (
                          <li key={i}>
                            <Image src={user} alt="user" />
                          </li>
                        ))}
                      </ul>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </main>
      </div>

      {/* --- Modals --- */}
      {showApplicationsSourcesModal && <ApplicationsSourcesModal onCancel={() => setShowApplicationsSourcesModal(false)} />}
    </div>
  );
}

export default Candidates;
