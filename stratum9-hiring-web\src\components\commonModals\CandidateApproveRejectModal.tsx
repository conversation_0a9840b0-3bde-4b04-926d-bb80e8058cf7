"use client";
import React, { FC, useState } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textarea from "../formElements/Textarea";
import { useForm } from "react-hook-form";
import { changeApplicationStatus } from "@/services/screenResumeServices";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";
import { toastMessageSuccess, toTitleCase } from "@/utils/helper";
import { CandidateApplication, topCandidateApplication } from "@/interfaces/candidatesInterface";

interface IProps {
  onClickCancel: () => void;
  disabled?: boolean;
  candidate?: CandidateApplication | topCandidateApplication;
  onSuccess?: (candidate: CandidateApplication | topCandidateApplication, status: string) => void;
}

const CandidateApproveRejectModal: FC<IProps> = ({ onClickCancel, candidate, onSuccess }) => {
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<(typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS]>(APPLICATION_STATUS.ON_HOLD);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<{ reason: string }>({
    defaultValues: {
      reason: "",
    },
    mode: "onSubmit",
    criteriaMode: "firstError",
    shouldFocusError: true,
    reValidateMode: "onChange",
    resolver: (values) => {
      const errors: Record<string, { type: string; message: string }> = {};

      // Required validation for reason field
      if (!values.reason || values.reason.trim() === "") {
        errors.reason = {
          type: "required",
          message: "Please provide a reason",
        };
      } else if (values.reason.trim().length < 5) {
        errors.reason = {
          type: "minLength",
          message: "Reason should be at least 5 characters long",
        };
      } else if (values.reason.trim().length > 50) {
        errors.reason = {
          type: "maxLength",
          message: "Reason should not exceed 50 characters",
        };
      }

      return {
        values,
        errors,
      };
    },
  });

  const onSubmit = async (formData: { reason: string }) => {
    if (!candidate || !authData) return;

    try {
      setIsSubmitting(true);
      setError("");

      const data = {
        job_id: candidate.job_id,
        candidate_id: candidate.candidateId,
        hiring_manager_id: authData.id,
        status: selectedStatus,
        hiring_manager_reason: formData.reason,
      };

      const response = await changeApplicationStatus(data);

      if (response.data && response.data.success) {
        toastMessageSuccess("Candidate status has been updated successfully!");
        setSuccess(true);
        // Call the onSuccess callback if provided
        if (onSuccess) {
          setTimeout(() => {
            onClickCancel();
            onSuccess(candidate, selectedStatus);
          }, 1500);
        }
      } else {
        setError(response.data?.message || "Failed to update candidate status");
      }
    } catch (err) {
      console.error("Error updating candidate status:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>Review Candidate</h2>
            <p>Please review the candidate's profile and make a decision.</p>
            {!isSubmitting && (
              <Button className="modal-close-btn" onClick={onClickCancel}>
                <ModalCloseIcon />
              </Button>
            )}
          </div>
          <div className="modal-body">
            {/* qualification-card */}
            <div className="qualification-card">
              <div className="qualification-card-top">
                <div className="name">
                  <h3>{toTitleCase(candidate?.candidateName || "Candidate")}</h3>
                  <p>{candidate?.aiDecision || "Pending"} by S9 Interviews</p>
                </div>
                <div className="top-right">
                  <div className="on-hold-status">
                    <p>{candidate?.applicationStatus}</p>
                  </div>
                </div>
              </div>
              <div className="qualification-card-mid">
                <p>
                  <b>Reasons why they are a good match:</b>
                </p>
                <p>{candidate?.aiReason || "No reason provided by AI evaluation."}</p>
              </div>
            </div>

            {!success && (
              <>
                {/* Decision selection moved to the submit buttons */}

                <form onSubmit={handleSubmit(onSubmit)}>
                  <InputWrapper>
                    <InputWrapper.Label htmlFor="reason" required>
                      Reason
                    </InputWrapper.Label>
                    <Textarea rows={6} name="reason" control={control} placeholder="Enter your reason here" className="form-control" />
                    {errors.reason && <p className="text-danger mt-1">{errors.reason.message as string}</p>}
                  </InputWrapper>

                  {error && <div className="error-message alert alert-danger my-3">{error}</div>}

                  <div className="action-btn gap-3 mt-4">
                    {isSubmitting ? (
                      <div className="text-center w-100">
                        <div className="spinner-border text-primary" role="status">
                          <span className="visually-hidden">Submitting...</span>
                        </div>
                        <p className="mt-2">Submitting...</p>
                      </div>
                    ) : (
                      <>
                        <Button
                          type="button"
                          className="primary-btn rounded-md w-100"
                          onClick={() => {
                            setSelectedStatus(APPLICATION_STATUS.APPROVED);
                            handleSubmit(onSubmit)();
                          }}
                        >
                          Approve Candidate
                        </Button>
                        <Button
                          type="button"
                          className="danger-btn rounded-md w-100"
                          onClick={() => {
                            setSelectedStatus(APPLICATION_STATUS.REJECTED);
                            handleSubmit(onSubmit)();
                          }}
                        >
                          Reject Candidate
                        </Button>
                      </>
                    )}
                  </div>
                </form>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CandidateApproveRejectModal;
