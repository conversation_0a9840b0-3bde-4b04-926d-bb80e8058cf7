"use client";
import React from "react";
import style from "@/styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import Link from "next/link";
import { useRouter } from "next/navigation";
import NewJobCreationIcon from "@/components/svgComponents/subscription/NewJobCreationIcon";
import RepostingJobIcon from "@/components/svgComponents/subscription/RepostingJobIcon";
import AIJobDescriptionIcon from "@/components/svgComponents/subscription/AIJobDescriptionIcon";
import MultiPlatformIcon from "@/components/svgComponents/subscription/MultiPlatformIcon";
import CustomApprovalIcon from "@/components/svgComponents/subscription/CustomApprovalIcon";
import ResumeScreeningIcon from "@/components/svgComponents/subscription/ResumeScreeningIcon";
import ResumeSourceIcon from "@/components/svgComponents/subscription/ResumeSourceIcon";
import PreScreeningIcon from "@/components/svgComponents/subscription/PreScreeningIcon";
import CandidateTrackingIcon from "@/components/svgComponents/subscription/CandidateTrackingIcon";
import InterviewSchedulingIcon from "@/components/svgComponents/subscription/InterviewSchedulingIcon";
import CheckedPlanIcon from "@/components/svgComponents/subscription/CheckedPlanIcon";

function ActivatePlan() {
  const router = useRouter();
  return (
    <div className={style.subscription_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="breadcrumb">
            <Link href="/">Home </Link>
            <Link href="/">Subscriptions</Link>
          </div>
          <div className="common-page-head-section">
            <div className="main-heading">
              <h2>
                Activate <span>Plan</span>
              </h2>
            </div>
          </div>
        </div>
        <div className="inner-content">
          <div className="row">
            <div className="col-md-3">
              <ul className={`${style.subscription_plan} ${style.side_bar} pe-4`}>
                <li className={style.plan_name}>Benefits</li>
                <li>
                  <NewJobCreationIcon /> New Job Creation
                </li>
                <li>
                  <RepostingJobIcon /> Reposting the Same Job
                </li>
                <li>
                  <AIJobDescriptionIcon /> AI Job Description
                </li>
                <li>
                  <MultiPlatformIcon /> Multi-Platform Job Posting
                </li>
                <li>
                  <CustomApprovalIcon /> Custom Approval of Resume
                </li>
                <li>
                  <ResumeScreeningIcon /> Resume Screening/job
                </li>
                <li>
                  <ResumeSourceIcon /> Resume Source Tracking
                </li>
                <li>
                  <PreScreeningIcon /> Pre-Screening Assessments per candidate
                </li>
                <li>
                  <CandidateTrackingIcon /> Candidate Tracking
                </li>
                <li>
                  <InterviewSchedulingIcon /> Interview Scheduling
                </li>
                <li>
                  <InterviewSchedulingIcon />
                  AI Interview Features
                </li>
                <li>
                  <InterviewSchedulingIcon />
                  Role-Based Access
                </li>
                <li>
                  <InterviewSchedulingIcon />
                  Real Time Follow Up
                </li>
                <li>
                  <InterviewSchedulingIcon />
                  Get Video Conference Interview
                </li>
              </ul>
            </div>
            <div className="col">
              <ul className={`${style.subscription_plan}`}>
                <li className={style.plan_name}>Free Trial</li>
                <li>1 Job Posting</li>
                <li>-</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>Limited</li>
                <li>50 Resumes</li>
                <li>-</li>
                <li className={style.sibling_height}>-</li>
                <li>Basic</li>
                <li>One Round</li>
                <li>-</li>
                <li>-</li>
                <li>-</li>
                <li>-</li>
              </ul>
            </div>
            <div className="col">
              <ul className={`${style.subscription_plan}`}>
                <li className={style.plan_name}>Basic Plan</li>
                <li>5 Job Posting</li>
                <li>-</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>Full</li>
                <li>1000 Resumes</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li className={style.sibling_height}>
                  5 Per <br /> Job
                </li>
                <li>Full</li>
                <li>Multiple Rounds</li>
                <li>-</li>
                <li>-</li>
                <li>-</li>
                <li>-</li>
              </ul>
            </div>
            <div className="col">
              <ul className={`${style.subscription_plan}`}>
                <li className={style.plan_name}>Professional Plan</li>
                <li>25 Job Posting</li>
                <li>-</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>Full</li>
                <li>50,000 Resumes</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li className={style.sibling_height}>
                  10 Per <br /> Job
                </li>
                <li>Full</li>
                <li>Multiple Rounds</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>-</li>
                <li>-</li>
              </ul>
            </div>
            <div className="col">
              <ul className={`${style.subscription_plan}`}>
                <li className={style.plan_name}>Enterprise Plan</li>
                <li>Unlimited Job Posting</li>
                <li>-</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>Full</li>
                <li>Unlimited Resumes</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li className={style.sibling_height}>Unlimited</li>
                <li>Full</li>
                <li>Unlimited Rounds</li>
                <li>
                  <CheckedPlanIcon />
                </li>
                <li>Fully Customizable</li>
                <li>-</li>
                <li>-</li>
              </ul>
            </div>

            <div className={`${style.select_plan_section}`}>
              <h3>Choose Plan</h3>
              <div className="row">
                <div className="col-md-3">
                  <div className={`${style.select_plan}`}>
                    <h4>Free</h4>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className={`${style.select_plan}`}>
                    <h4>Basic</h4>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className={`${style.select_plan} ${style.active}`}>
                    <h4>Professional</h4>
                  </div>
                </div>
                <div className="col-md-3">
                  <div className={`${style.select_plan}`}>
                    <h4>Enterprise</h4>
                  </div>
                </div>
              </div>
            </div>
            <div className={`${style.select_plan_section}`}>
              <h3>Choose Subscription Type</h3>
              <div className="row">
                <div className="col-md-4">
                  <div className={`${style.select_plan} ${style.active}`}>
                    <div>
                      <h4>$4.99 Monthly</h4>
                      <p>Full Access</p>
                    </div>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className={`${style.select_plan} ${style.flex_content}`}>
                    <div>
                      <h4>$12.99 Quarterly</h4>
                      <p>Approx $4.33 per month</p>
                    </div>
                    <span>Save 13% </span>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className={`${style.select_plan} ${style.flex_content}`}>
                    <div>
                      <h4>$44.99 Yearly</h4>
                      <p>Approx $3.75 per month</p>
                    </div>
                    <span>Save 25% </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="button-align py-5 ">
          <Button
            className="primary-btn rounded-md"
            onClick={() => {
              router.push("/interview-mode");
            }}
          >
            Subscribe to Monthly Plan
          </Button>
          <Button className="dark-outline-btn rounded-md">Cancel</Button>
        </div>
      </div>
    </div>
  );
}

export default ActivatePlan;
