"use client";
import React, { useState, useEffect, useCallback } from "react";
import styles from "../../../styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import EditIcon from "@/components/svgComponents/EditIcon";
import DeleteIcon from "@/components/svgComponents/DeleteIcon";
import Image from "next/image";
import userRolesImg from "../../../../public/assets/images/user-role-img.png";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import UserRoleModal from "@/components/commonModals/UserRoleModal";
import EditPermissionsModal from "@/components/commonModals/EditPermissionsModal";
import { findRole, FindRoleResponse, getRolePermissions } from "@/services/roleService";
import { IRolePermission } from "@/interfaces/roleInterface";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { useTranslations } from "next-intl";
import CommonTableSkelton from "./CommonTableSkelton";
import RefreshAlertIcon from "@/components/svgComponents/RefreshAlertIcon";
import Skeleton from "react-loading-skeleton";

// Interface for role permission data
export const ROLE_ALTER_MODE = {
  ADD: "add",
  EDIT: "edit",
  DELETE: "delete",
};

export const VIEW_MODE = {
  ROLES: "roles",
  PERMISSIONS: "permissions",
};

const UserRoles = () => {
  const t = useTranslations();
  const { control, watch } = useForm();
  const [roleModalConfig, setRoleModalConfig] = useState<{
    show: boolean;

    mode: (typeof ROLE_ALTER_MODE)[keyof typeof ROLE_ALTER_MODE];
    role: FindRoleResponse | null;
  }>({ show: false, mode: ROLE_ALTER_MODE.ADD, role: null });
  const [showEditPermissionsModal, setShowEditPermissionsModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<{ id: number; name: string } | null>(null);
  const [roles, setRoles] = useState<FindRoleResponse[]>([]);
  const [rolePermissions, setRolePermissions] = useState<IRolePermission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<(typeof VIEW_MODE)[keyof typeof VIEW_MODE]>(VIEW_MODE.ROLES);

  const [searchTerm, setSearchTerm] = useState<string>("");

  // Watch the search input field
  const searchValue = watch("search");

  // Define fetch functions with useCallback to prevent unnecessary re-renders
  const fetchRoles = useCallback(async () => {
    try {
      setIsLoading(true);
      setLoadError("");
      const response = await findRole();
      if (response.data?.success) {
        setRoles(response.data.data);
      } else {
        console.error("Failed to load roles:", response);
        setLoadError(t("failed_load_roles"));
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
      setLoadError(t("unexpected_error"));
    } finally {
      setIsLoading(false);
    }
  }, [t]);

  const fetchRolePermissions = useCallback(
    async (search = "") => {
      try {
        setIsLoading(true);
        setLoadError("");
        const response = await getRolePermissions(search);
        if (response.data?.success) {
          setRolePermissions(response.data.data);
        } else {
          toastMessageError(response?.data?.message ? response.data.message : t("failed_load_roles"));
          setLoadError(t("failed_load_roles"));
        }
      } catch (error) {
        console.error(error);
        toastMessageError(t("unexpected_error"));
        setLoadError(t("unexpected_error"));
      } finally {
        setIsLoading(false);
      }
    },
    [t]
  );

  // Fetch data when component mounts or view changes
  useEffect(() => {
    if (activeView === VIEW_MODE.ROLES) {
      fetchRoles();
    } else {
      fetchRolePermissions(searchTerm);
    }
  }, [activeView, fetchRoles, fetchRolePermissions, searchTerm]);

  // Debounced search effect
  useEffect(() => {
    if (activeView !== VIEW_MODE.PERMISSIONS) return;

    const timer = setTimeout(() => {
      if (searchValue !== undefined) {
        setSearchTerm(searchValue);
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timer);
  }, [searchValue, activeView]);

  // Effect to fetch role permissions when search term changes
  useEffect(() => {
    if (activeView === VIEW_MODE.PERMISSIONS) {
      fetchRolePermissions(searchTerm);
    }
  }, [searchTerm, activeView, fetchRolePermissions]);

  const openRoleModal = useCallback(
    (mode: (typeof ROLE_ALTER_MODE)[keyof typeof ROLE_ALTER_MODE], role: { id: number; name: string; isDefaultRole: boolean } | null = null) => {
      setRoleModalConfig({
        show: true,
        mode,
        role,
      });
    },
    []
  );

  const closeRoleModal = () => {
    setRoleModalConfig({
      show: false,
      mode: ROLE_ALTER_MODE.ADD,
      role: null,
    });
  };

  const handleRoleSuccess = (message?: string) => {
    // Refresh the roles list
    fetchRoles();

    // Show success toast notification if message is provided
    if (message) {
      toastMessageSuccess(message);
    }
  };

  const handleEditClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {
    openRoleModal(ROLE_ALTER_MODE.EDIT, role);
  };

  const handleDeleteClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {
    openRoleModal(ROLE_ALTER_MODE.DELETE, role);
  };

  const handlePermissionsClick = (role: { id: number; name: string; isDefaultRole: boolean }) => {
    setSelectedRole(role);
    setShowEditPermissionsModal(true);
  };

  const handlePermissionsSuccess = (message?: string) => {
    // Refresh the appropriate data based on active view
    if (activeView === VIEW_MODE.ROLES) {
      fetchRoles();
    } else {
      fetchRolePermissions(searchTerm);
    }

    // Show success toast notification if message is provided
    if (message) {
      toastMessageSuccess(message);
    }
  };

  return (
    <>
      <section className={styles.access_management}>
        <div className="container">
          <div className="row">
            <div className={activeView === VIEW_MODE.ROLES ? "col-md-7" : "col-md-12"}>
              <div className="common-page-head-section">
                <div className="main-heading">
                  <h2>
                    {t("access_management", { defaultValue: "Access" })} <span>{t("management", { defaultValue: "Management" })}</span>
                  </h2>
                </div>
              </div>
              <div className="button-align justify-content-between mt-4 mb-5">
                <div className="button-align">
                  <Button
                    className={`${activeView === VIEW_MODE.ROLES ? "primary-btn" : "dark-outline-btn"} rounded-md button-sm`}
                    onClick={() => setActiveView(VIEW_MODE.ROLES)}
                  >
                    {t("user_roles", { defaultValue: "User Roles" })}
                  </Button>
                  <Button
                    className={`${activeView === VIEW_MODE.PERMISSIONS ? "primary-btn" : "dark-outline-btn"} rounded-md button-sm`}
                    onClick={() => setActiveView(VIEW_MODE.PERMISSIONS)}
                  >
                    {t("user_permissions", { defaultValue: "User Permissions" })}
                  </Button>
                </div>

                {activeView === VIEW_MODE.PERMISSIONS && (
                  <InputWrapper className="mb-0 w-50">
                    <div className="icon-align right">
                      <Textbox
                        className="form-control w-100"
                        control={control}
                        name="search"
                        type="text"
                        placeholder={t("search_user_role", { defaultValue: "Search using user role" })}
                      >
                        <InputWrapper.Icon>
                          <SearchIcon />
                        </InputWrapper.Icon>
                      </Textbox>
                    </div>
                  </InputWrapper>
                )}
              </div>

              {activeView === VIEW_MODE.ROLES ? (
                <div className="common-card margin-add">
                  <div className="card-header">
                    <h3>{t("user_role")}</h3>
                    <Button className="primary-btn rounded-md" onClick={() => openRoleModal(ROLE_ALTER_MODE.ADD)}>
                      {t("add_new_role")}
                    </Button>
                  </div>
                  <div className="card-body">
                    {isLoading ? (
                      <ul className={`mt-3 ${styles.user_roles}`}>
                        <Skeleton height={55} width="100%" count={6} borderRadius={4} style={{ margin: "10px 0" }} />
                      </ul>
                    ) : loadError ? (
                      <div className="alert alert-danger text-center mt-3">
                        <p> {loadError}</p>
                        <Button className="clear-btn p-0 m-auto" onClick={fetchRoles}>
                          <RefreshAlertIcon />
                        </Button>
                      </div>
                    ) : roles.length === 0 ? (
                      <div className="text-center py-4">
                        <p>{t("no_roles_found")}</p>
                      </div>
                    ) : (
                      <ul className={styles.user_roles}>
                        {roles.map((role) => (
                          <li key={role.id} className={role.isDefaultRole ? styles.disabled_role : ""}>
                            <p>{role.name}</p>
                            <div className="button-align">
                              <Button
                                className={`clear-btn p-0 ${role.isDefaultRole ? "disabled" : ""}`}
                                onClick={() => !role.isDefaultRole && handleEditClick(role)}
                                disabled={role.isDefaultRole}
                              >
                                <EditIcon />
                              </Button>
                              <Button
                                className={`clear-btn p-0 ${role.isDefaultRole ? "disabled" : ""}`}
                                onClick={() => !role.isDefaultRole && handleDeleteClick(role)}
                                disabled={role.isDefaultRole}
                              >
                                <DeleteIcon />
                              </Button>
                            </div>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                </div>
              ) : (
                <div className="table-responsive mt-5">
                  <table className="table">
                    <thead>
                      <tr>
                        <th className="w-25">{t("user_role")}</th>
                        <th className="w-25">{t("permission_counts")}</th>
                        <th className="w-25">{t("last_modified")}</th>
                        <th className="w-25">{t("actions")}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {isLoading ? (
                        <CommonTableSkelton Rowcount={20} ColumnCount={4} ColumnWidth="25%" />
                      ) : loadError ? (
                        <tr>
                          <td colSpan={4}>
                            <div className="alert alert-danger text-center mt-3">
                              <p> {loadError}</p>
                              <Button className="clear-btn p-0 m-auto" onClick={fetchRoles}>
                                <RefreshAlertIcon />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ) : roles.length === 0 ? (
                        <tr>
                          <td colSpan={4} className="text-center py-4">
                            {t("no_roles_found")}
                          </td>
                        </tr>
                      ) : (
                        rolePermissions.map((role) => (
                          <tr key={role.id} className={role.isDefaultRole ? "text-muted opacity-75 disabled-row" : ""}>
                            <td>{role.name}</td>
                            <td>{role.permission_count}</td>
                            <td>{new Date(role.updated_ts).toLocaleDateString("en-US", { month: "2-digit", day: "2-digit", year: "numeric" })}</td>
                            <td align="center">
                              <Button
                                className={`clear-btn p-0 text-decoration-underline ${role.isDefaultRole ? "text-muted" : "color-primary"}`}
                                onClick={() =>
                                  !role.isDefaultRole && handlePermissionsClick({ id: role.id, name: role.name, isDefaultRole: role.isDefaultRole })
                                }
                                disabled={role.isDefaultRole}
                                style={role.isDefaultRole ? { cursor: "not-allowed" } : {}}
                              >
                                {t("edit")}
                              </Button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {activeView === VIEW_MODE.ROLES && (
              <div className="col-md-5">
                <Image src={userRolesImg} alt={t("user_roles_alt")} className={styles.user_roles_img} />
              </div>
            )}
          </div>
        </div>
      </section>

      {roleModalConfig.show && (
        <UserRoleModal
          onClickCancel={closeRoleModal}
          onSubmitSuccess={handleRoleSuccess}
          role={roleModalConfig.role}
          mode={roleModalConfig.mode}
          disabled={isLoading}
        />
      )}

      {showEditPermissionsModal && selectedRole && (
        <EditPermissionsModal
          onClickCancel={() => setShowEditPermissionsModal(false)}
          onSubmitSuccess={handlePermissionsSuccess}
          role={selectedRole}
          disabled={isLoading}
        />
      )}
    </>
  );
};

export default UserRoles;
