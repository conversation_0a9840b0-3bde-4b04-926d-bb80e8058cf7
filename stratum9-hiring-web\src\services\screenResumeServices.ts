import endpoint from "@/constants/endpoint";
import { http } from "@/utils/http";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import { IFormValues } from "@/interfaces/screenResumeInterfaces";
import { APPLICATION_STATUS } from "@/constants/jobRequirementConstant";

// Define interface for pagination parameters
interface PaginationParams {
  limit?: number;
  offset?: number;
  job_id?: number;
  status?: string;
  hiring_manager_id?: number;
  organization_id?: number;
}

// Define interfaces for API responses
interface ManualUploadResponse {
  success: boolean;
  message: string;
  data: {
    name: string;
    email: string;
    gender: string;
    additional_details: string;
    resume_file: string;
    resume_text: string;
    assessment_file: string;
    assessment_text: string;
  };
}

interface JobApplication {
  application_id: number;
  job_id: number;
  hiring_manager_id: number;
  candidate_id: number;
  candidate_name: string;
  ai_decision: string;
  ai_reason: string;
  status?: string;
  created_ts: string;
}

interface JobApplicationResponse {
  success: boolean;
  message: string;
  data: JobApplication[];
  pagination: {
    limit: number;
    offset: number;
    totalCount: number;
    hasMore: boolean;
  };
}

interface ChangeApplicationStatusParams {
  job_id: number;
  candidate_id: number;
  hiring_manager_id: number;
  status: (typeof APPLICATION_STATUS)[keyof typeof APPLICATION_STATUS];
  hiring_manager_reason: string;
}

interface ChangeApplicationStatusResponse {
  success: boolean;
  message: string;
  data: JobApplication;
}

/**
 * Upload resume and assessment files and get presigned URLs
 * @param file - The file to upload (resume or assessment)
 * @returns Promise with presigned URL response
 */
export const getPresignedUrl = async (file: File): Promise<ApiResponse> => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("fileType", file.type);
  formData.append("fileName", file.name);

  return http.post(endpoint.resumeScreen.GET_PRESIGNED_URL, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

/**
 * Upload file to S3 using presigned URL
 * @param presignedUrl - The presigned URL for S3 upload
 * @param file - The file to upload
 * @returns Promise with upload response
 */
export const uploadToS3 = async (presignedUrl: string, file: File): Promise<Response> => {
  return fetch(presignedUrl, {
    method: "PUT",
    body: file,
    headers: {
      "Content-Type": file.type,
    },
  });
};

/**
 * Process the file upload to get presigned URL and upload to S3
 * @param file - The file to upload
 * @returns Object with file URL and parsed text
 */
export const processFileUpload = async (file: File): Promise<{ fileUrl: string; fileText: string; presignedUrl: string }> => {
  try {
    // Get presigned URL
    const presignedUrlResponse = await getPresignedUrl(file);

    if (!presignedUrlResponse.data) {
      throw new Error("Failed to get presigned URL");
    }
    const responseData = presignedUrlResponse.data;

    // The response might have data nested inside another data property
    const urlData = responseData.data;

    if (!urlData.presignedUrl || !urlData.fileUrl) {
      console.error("Missing URL information in response:", urlData);
      throw new Error("Missing URL information in response");
    }

    const { presignedUrl, fileUrl, fileText } = urlData;

    // Upload file to S3
    const uploadResponse = await uploadToS3(presignedUrl, file);
    if (!uploadResponse.ok) {
      throw new Error(`Failed to upload file to S3: ${uploadResponse.status}`);
    }
    // Return the file URL and flag for backend extraction
    return {
      fileUrl,
      fileText: fileText, // Special flag to indicate backend should extract text
      presignedUrl,
    };
  } catch (error) {
    console.error("Error processing file upload:", error);
    // Include error details in the console for debugging
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    throw error;
  }
};

/**
 * Upload manual candidate data with resume and assessment
 * @param data - The form values with candidate information
 * @returns Promise with API response
 */
/**
 * Get all job applications with pagination (not just pending)
 * @param params - Pagination parameters (limit, offset, filters)
 * @returns Promise with job applications response
 */
export const getAllPendingJobApplications = async (params: PaginationParams): Promise<ApiResponse<JobApplicationResponse>> => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append("limit", params.limit.toString());
    // Always include offset parameter, even when it's 0
    queryParams.append("offset", params.offset !== undefined ? params.offset.toString() : "0");
    if (params.job_id) queryParams.append("job_id", params.job_id.toString());
    if (params.status) queryParams.append("status", params.status);
    if (params.hiring_manager_id) queryParams.append("hiring_manager_id", params.hiring_manager_id.toString());
    if (params.organization_id) queryParams.append("organization_id", params.organization_id.toString());

    // Make API request
    const url = `${endpoint.resumeScreen.GET_ALL_PENDING_JOB_APPLICATIONS}?${queryParams.toString()}`;
    return http.get(url);
  } catch (error) {
    console.error("Error fetching job applications:", error);
    throw error;
  }
};

export const uploadManualCandidate = async (data: IFormValues, jobId: number): Promise<ApiResponse<ManualUploadResponse>> => {
  try {
    // Process candidates with file uploads
    const processedCandidates = await Promise.all(
      data.candidates.map(async (candidate) => {
        // Process resume file
        const resumeResult = candidate.resume ? await processFileUpload(candidate.resume as File) : { presignedUrl: "", fileUrl: "", fileText: "" };
        // Process assessment file
        const assessmentResult = candidate.assessment
          ? await processFileUpload(candidate.assessment as File)
          : { presignedUrl: "", fileUrl: "", fileText: "" };
        return {
          name: candidate.name,
          email: candidate.email,
          gender: candidate.gender,
          additional_details: candidate.additionalInfo || "",
          resume_file: resumeResult.presignedUrl,
          resume_text: resumeResult.fileText,
          assessment_file: assessmentResult.presignedUrl,
          assessment_text: assessmentResult.fileText,
        };
      })
    );

    // Create payload for API
    const payload = {
      job_id: jobId,
      candidates: processedCandidates,
    };

    // Send to backend API
    return http.post(endpoint.resumeScreen.MANUAL_CANDIDATE_UPLOAD, payload);
  } catch (error) {
    console.error("Error in uploadManualCandidate:", error);
    throw error;
  }
};

/**
 * Change the status of a job application (Approve, Reject, or Hold)
 * @param params - Parameters containing job_id, candidate_id, hiring_manager_id, and status
 * @param data - Data containing hiring_manager_reason
 * @returns Promise with API response
 */
export const changeApplicationStatus = async (data: ChangeApplicationStatusParams): Promise<ApiResponse<ChangeApplicationStatusResponse>> => {
  return http.post(endpoint.resumeScreen.CHANGE_APPLICATION_STATUS, data);
};
