import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import {
  AdditionalInfoPayload,
  CandidateApplication,
  CandidateProfileResponse,
  PromoteDemotePayload,
  topCandidateApplication,
} from "@/interfaces/candidatesInterface";

/**
 * Fetch candidates with their job applications.
 *
 * Automatically sends query parameters to backend, including:
 * - jobId: Filter by job
 * - searchStr: For candidate name search
 * - isActive: true for active, false for archived
 * - page: For pagination (used as offset)
 * - limit: Number of items per page
 */
export const fetchCandidatesApplications = (data: {
  page?: number; // Offset for pagination
  limit?: number; // Max results per page
  searchStr?: string; // Search candidates by name
  isActive?: boolean; // Filter: true = active, false = archived
  jobId?: number; // Optional jobId filter
}): Promise<ApiResponse<CandidateApplication[]>> => {
  return http.get(endpoint.candidatesApplication.GET_CANDIDATES_WITH_APPLICATIONS, { ...data });
};

export const fetchTopCandidatesApplications = (jobId?: number): Promise<ApiResponse<topCandidateApplication[]>> => {
  return http.get(endpoint.candidatesApplication.GET_TOP_CANDIDATES_WITH_APPLICATIONS, {
    jobId, // Optional jobId filter
  });
};

export const promoteDemoteCandidate = async (payload: PromoteDemotePayload): Promise<ApiResponse<null>> => {
  return await http.put(endpoint.candidatesApplication.PROMOTE_DEMOTE_CANDIDATE, payload);
};

export const addApplicantAdditionalInfo = async (payload: AdditionalInfoPayload): Promise<ApiResponse<null>> => {
  return await http.post(endpoint.candidatesApplication.ADDITIONAL_INFO, payload);
};

/**
 * Fetches candidate profile details by candidate ID
 * @param candidateId - The ID of the candidate
 */
export const fetchCandidateProfile = (candidateId: number | string): Promise<ApiResponse<CandidateProfileResponse>> => {
  return http.get(endpoint.candidatesApplication.GET_CANDIDATE_DETAILS, { candidateId });
};

/**
 * Updates the job application status (Hire/Reject)
 * @param jobApplicationId - The ID of the job application to update
 * @param payload - The status payload ("Final-Hired" or "Rejected")
 */
export const updateJobApplicationStatus = async (jobApplicationId: number, status: string): Promise<ApiResponse<null>> => {
  return await http.put(endpoint.candidatesApplication.UPDATE_JOB_APPLICATION_STATUS.replace(":jobApplicationId", jobApplicationId.toString()), {
    status,
  });
};
