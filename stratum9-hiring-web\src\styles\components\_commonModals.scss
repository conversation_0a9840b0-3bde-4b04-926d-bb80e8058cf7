@use "../abstracts" as *;

.theme-modal {
  backdrop-filter: blur(3px);
  background: rgba(#436eb6, 0.5);
  &.show-modal {
    display: block;
  }

  .modal-dialog.modal-xl {
    max-width: 1000px;
    width: 90%;
  }
  .modal-dialog {
    .modal-content {
      border-radius: 16px;
      background-color: $white;
      border: 0px;
      box-shadow:
        0px 7px 2px 0px rgba(0, 0, 0, 0),
        0px 5px 2px 0px rgba(0, 0, 0, 0.01),
        0px 3px 2px 0px rgba(0, 0, 0, 0.02),
        0px 1px 1px 0px rgba(0, 0, 0, 0.03),
        0px 0px 1px 0px rgba(0, 0, 0, 0.04);

      .modal-header {
        border: 0px;
        padding: 20px;
        position: relative;
        display: block;
        text-align: center;
        &.secondary-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 20px;
          border-bottom: 1px solid rgba($dark, 0.1);
        }

        h2 {
          color: #212121;
          font-size: 32px;
          line-height: normal;
          margin: 15px 0px 15px;
          padding: 0px;
          font-weight: 600;
        }
        h4 {
          color: #212121;
          font-size: 26px;
          line-height: normal;
          margin: 10px 0;
          padding: 0px;
          font-weight: 600;
          span {
            color: $primary;
          }
        }
        p {
          max-width: 70%;
          margin: 0 auto;
          color: $dark;
          font-size: 14px;
          font-weight: 500;
          &.textMd {
            font-size: $text-md;
          }
          &.w100 {
            width: 100%;
            max-width: 100%;
          }
        }

        .modal-close-btn {
          padding: 0px;
          background-color: transparent;
          margin: 0px;
          position: absolute;
          top: -13px;
          right: -9px;
          border: 0px;
          z-index: 100;
          svg {
            width: 30px;
            height: 30px;
            min-width: 30px;
          }
        }
      }

      .modal-body {
        padding: 20px;

        .information-preview {
          .preview-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 18px;
          }

          .preview-section {
            margin-bottom: 16px;

            .preview-section-title {
              font-size: 20px;
              font-weight: 600;
              margin-bottom: 12px;
              color: $primary;
            }

            .preview-list {
              padding-left: 20px;

              .preview-item {
                font-size: 16px;
                margin-bottom: 8px;
                font-weight: 500;
              }
            }
          }
        }

        .alert-warning {
          font-size: 16px;
          line-height: 1.5;
          font-weight: 500;
        }

        .action-btn {
          display: flex;
          align-items: center;
          gap: 15px;

          .theme-button {
            min-width: 120px;
            font-size: 16px;
          }
        }
      }
    }
  }
  &.applications-sources-modal {
    .modal-body {
      .applications-list {
        margin-bottom: 10px;
        .item {
          display: flex;
          align-items: center;
          gap: 15px;
          justify-content: space-between;
          padding: 10px 0px;
          border-bottom: 1px solid rgba($black, 0.2);
          &:nth-last-child(1) {
            border-bottom: none;
          }
          .left-item {
            color: $dark;
            font-size: 16px;
            font-weight: 500;
            img {
              height: 30px;
              object-fit: contain;
            }
          }
          .item-right {
            color: $dark;
            font-size: 16px;
            font-weight: 500;
          }
        }
      }
    }
  }
  .interview-info-img {
    width: 110px;
    height: 180px;
    object-fit: contain;
    position: absolute;
    top: -60px;
    right: 60px;
    z-index: 0;
  }
  .copy-link-icon {
    width: 20px;
    height: 15px;
    fill: #333;
    margin-right: 10px;
  }
  .model-heading-lottie {
    // display: flex;
    // align-items: center;
    // justify-content: center;
    position: relative;
    .lottie-icon {
      width: 100px;
      height: 80px;
      object-fit: contain;
      position: absolute;
      top: -20px;
      right: 80px;
    }
  }
}
.interview-details-modal {
  p {
    font-size: $text-md;
    font-weight: $regular;
    color: $dark;
    margin: 0;
    margin-bottom: 10px;
    line-height: 1;
  }
  h4 {
    font-size: $text-md;
    font-weight: $semiBold;
    color: $dark;
    margin: 0;
    margin-bottom: 10px;
  }
  h5 {
    font-size: $text-md;
    font-weight: $semiBold;
    color: $dark;
    margin: 0;
    line-height: 1;
  }
  .high-light-text {
    background: rgba($secondary, 0.3);
    color: $dark;
    padding: 7px 12px;
    border-radius: 8px;
    font-size: $text-sm;
    display: inline-block;
    line-height: 1;
  }
  a {
    font-size: $text-md;
    color: $primary;
    line-height: 1;
  }
  sup {
    color: $danger;
  }
}
