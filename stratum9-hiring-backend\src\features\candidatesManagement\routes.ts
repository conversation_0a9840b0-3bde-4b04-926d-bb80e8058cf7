// src/features/candidatesManagement/routes.ts
import express from "express";
import auth from "../../middleware/auth";
import { ROUTES } from "../../utils/constants";
import {
  addApplicantAdditionalInfoController,
  getCandidateDetailsController,
  getCandidatesController,
  getTopCandidatesController,
  promoteDemoteCandidateController,
  archiveActiveApplication,
  updateJobApplicationStatusController,
} from "./controller";
import HandleErrors from "../../middleware/handleError";
import { authorizedForArchiveRestoreCandidates } from "../../middleware/isAuthorized";
import {
  queryValidation,
  schemaValidation,
  paramsValidation,
} from "../../middleware/validateSchema";
import {
  addApplicantAdditionalInfoValidation,
  getAllCandidatesValidation,
  getCandidateDetailsValidation,
  getTopCandidatesValidation,
  promoteDemoteCandidateValidation,
  archiveActiveApplicationValidation,
  updateJobApplicationStatusValidation,
  updateJobApplicationIdValidation,
} from "./validation";

const candidatesRoute = express.Router();

candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATES,
  auth,
  queryValidation(getAllCandidatesValidation),
  HandleErrors(getCandidatesController)
);

candidatesRoute.put(
  ROUTES.CANDIDATES.ARCHIVE_ACTIVE_APPLICATION,
  auth,
  authorizedForArchiveRestoreCandidates,
  schemaValidation(archiveActiveApplicationValidation),
  HandleErrors(archiveActiveApplication)
);

candidatesRoute.get(
  ROUTES.CANDIDATES.GET_TOP_CANDIDATES,
  auth,
  queryValidation(getTopCandidatesValidation),
  HandleErrors(getTopCandidatesController)
);

candidatesRoute.put(
  ROUTES.CANDIDATES.PROMOTE_DEMOTE_CANDIDATE,
  auth,
  schemaValidation(promoteDemoteCandidateValidation),
  HandleErrors(promoteDemoteCandidateController)
);

candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATE_DETAILS,
  auth,
  queryValidation(getCandidateDetailsValidation),
  HandleErrors(getCandidateDetailsController)
);

candidatesRoute.post(
  ROUTES.CANDIDATES.ADD_APPLICANT_ADDITIONAL_INFO,
  auth,
  schemaValidation(addApplicantAdditionalInfoValidation),
  HandleErrors(addApplicantAdditionalInfoController)
);

candidatesRoute.put(
  ROUTES.CANDIDATES.UPDATE_JOB_APPLICATION_STATUS,
  auth,
  paramsValidation(updateJobApplicationIdValidation),
  schemaValidation(updateJobApplicationStatusValidation),
  HandleErrors(updateJobApplicationStatusController)
);
export default candidatesRoute;
