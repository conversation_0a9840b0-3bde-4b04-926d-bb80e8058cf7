"use client";
import PreInterviewQuestionsOverview from "@/components/views/conductInterview/PreInterviewQuestionsOverview";
import React from "react";

const page = ({
  searchParams,
}: {
  searchParams: Promise<{ interviewId: string; jobApplicationId: string; interviewType: string; resumeLink: string; isEnded: string; date: string }>;
}) => {
  return (
    <div>
      <PreInterviewQuestionsOverview params={searchParams} />
    </div>
  );
};

export default page;
