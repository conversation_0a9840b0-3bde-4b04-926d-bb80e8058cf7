import endpoint from "@/constants/endpoint";
import * as http from "@/utils/http";
import { ApiResponse, IApiResponseCommonInterface } from "@/interfaces/commonInterfaces";
import { AddEmployeeResponseData, EmployeeInterface } from "@/interfaces/employeeInterface";
import { DEFAULT_LIMIT } from "@/constants/commonConstants";

/**
 * Add new employees to the system
 * @param employees Array of employee data to add
 * @returns Promise with API response
 */
// Define interface for the transformed employee data that matches backend validation
interface EmployeePayload {
  firstName: string;
  lastName: string;
  email: string;
  departmentId: number;
  roleId: number;
  // interviewOrder: number;
}

export const addEmployees = (employees: EmployeePayload[]): Promise<IApiResponseCommonInterface<AddEmployeeResponseData>> => {
  return http.post(endpoint.employee.ADD_EMPLOYEES, { employees });
};

/**
 * Get employees by department ID
 * @param departmentId ID of the department to fetch employees for
 * @param page Optional page number for pagination
 * @param limit Optional maximum number of records to return
 * @param search Optional search term
 * @returns Promise with API response containing employee data
 */
export const getEmployeesByDepartment = (
  departmentId: number,
  page: number = 1,
  limit: number = DEFAULT_LIMIT,
  search?: string
): Promise<IApiResponseCommonInterface<EmployeeInterface>> => {
  return http.get(endpoint.employee.GET_EMPLOYEES_BY_DEPARTMENT, {
    departmentId,
    page,
    limit,
    search,
  });
};

/**
 * Update employee role
 * @param employeeId ID of the employee to update
 * @param roleId ID of the new role
 * @returns Promise with API response
 */
export const updateEmployeeRole = (employeeId: number, roleId: number): Promise<ApiResponse> => {
  // Replace the :employeeId placeholder in the URL
  const baseUrl = endpoint.employee.UPDATE_EMPLOYEE_ROLE.replace(":employeeId", employeeId.toString());
  return http.put(baseUrl, {
    roleId,
  });
};

/**
 * Update employee interview order
 * @param employeeId ID of the employee to update
 * @param interviewOrder New interview order value
 * @returns Promise with API response
 */
export const updateEmployeeInterviewOrder = (employeeId: number, newInterviewOrder: number): Promise<ApiResponse> => {
  // Replace the :employeeId placeholder in the URL
  const baseUrl = endpoint.employee.UPDATE_EMPLOYEE_INTERVIEW_ORDER.replace(":employeeId", employeeId.toString());
  return http.put(baseUrl, {
    newInterviewOrder,
  });
};
