import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinC<PERSON>umn,
  UpdateDateColumn,
  CreateDateColumn,
} from "typeorm";
import CandidatesModel from "./candidates";
import { JobsModel } from "./jobs";
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
enum Source {
  FIVERR = "Fiverr",
  LINKEDIN = "LinkedIn",
  INDEED = "Indeed",
  OTHER = "Other",
}

enum AIDecision {
  APPROVED = "Approved",
  REJECTED = "Rejected",
}

export enum Status {
  APPROVED = "Approved",
  REJECTED = "Rejected",
  ON_HOLD = "On-Hold",
  HIRED = "Hired",
  PENDING = "Pending",
  FINAL_REJECT = "Final-Reject",
}

export enum ApplicationRankStatus {
  PROMOTED = "Promoted",
  DEMOTED = "Demoted",
  NO_CHANGES = "No Changes",
}

@Entity("job_applications")
export default class JobApplicationsModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true, name: "hiring_manager_id" })
  hiringManagerId: number;

  @ManyToOne(() => JobsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "job_id" })
  job: JobsModel;

  @Column()
  jobId: number;

  @ManyToOne(() => CandidatesModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "candidate_id" })
  candidate: CandidatesModel;

  @Column({ name: "candidate_id", nullable: false })
  candidateId: number;

  @Column({ length: 150, nullable: false, name: "resume_file" })
  resumeFile: string;

  @Column({ type: "text", nullable: false, name: "resume_text" })
  resumeText: string;

  @Column({ length: 150, nullable: false, name: "assessment_file" })
  assessmentFile: string;

  @Column({ type: "text" })
  assessmentText: string;

  @Column({ type: "text", nullable: true, name: "additional_details" })
  additionalDetails: string;

  @Column({
    type: "enum",
    enum: Source,
    name: "source",
    nullable: true,
  })
  source: Source;

  @Column({ type: "json", nullable: true, name: "ats_score" })
  atsScore: object;

  @Column({
    type: "enum",
    enum: AIDecision,
    nullable: true,
    name: "ai_decision",
  })
  aiDecision: AIDecision;

  @Column({ length: 500, nullable: true, name: "ai_reason" })
  aiReason: string;

  @Column({
    type: "enum",
    enum: Status,
    nullable: true,
    default: Status.PENDING,
  })
  status: Status;

  @Column({ default: false, name: "is_top_application" })
  isTopApplication: boolean;

  @Column({
    type: "enum",
    enum: ApplicationRankStatus,
    nullable: false,
    default: ApplicationRankStatus.NO_CHANGES,
    name: "application_rank_status",
  })
  applicationRankStatus: ApplicationRankStatus;

  @Column({ length: 50, nullable: true, name: "hiring_manager_reason" })
  hiringManagerReason: string;

  @Column({ default: true, name: "is_active" })
  isActive: boolean;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_ts",
    nullable: false,
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
    nullable: false,
  })
  updatedTs: Date;
}
