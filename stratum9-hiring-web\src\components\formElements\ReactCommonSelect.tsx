/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from "react";
import { Controller, Control, FieldValues, Path, FieldErrors } from "react-hook-form";
import Select from "react-select";

// Define option type
export interface SelectOption {
  value: number;
  label: string;
  [key: string]: any;
}

interface CommonSelectProps<T extends FieldValues = FieldValues> {
  name: Path<T>;
  control?: Control<T>;
  options: readonly SelectOption[] | SelectOption[];
  label?: string;
  placeholder?: string;
  isDisabled?: boolean;
  isClearable?: boolean;
  isSearchable?: boolean;
  classNamePrefix?: string;
  // value?: SelectOption | null;
  onChange?: (value: SelectOption | null) => void;
  onInputChange?: (value: string) => void;
  errors?: FieldErrors<T>;
  isLoading?: boolean;
}

const ReactCommonSelect = <T extends FieldValues = FieldValues>({
  name,
  control,
  options,
  label,
  placeholder,
  isDisabled = false,
  isClearable = true,
  isSearchable = true,
  classNamePrefix = "select",
  // value,
  onInputChange,
  onChange,
  errors,
  isLoading,
}: CommonSelectProps<T>) => {
  const [isSelecting, setIsSelecting] = useState(false);
  return (
    <div className="form-group">
      {label && <label className="form-label">{label}</label>}
      <Controller
        name={name}
        control={control}
        render={({ field }) => {
          console.log("field", field);
          const selectedOption = field.value === 0 ? null : options.find((option) => option.value === field.value) || null;

          // console.log("selectedOption", selectedOption)
          return (
            <>
              <Select
                {...field}
                className="form-control py-1 px-0"
                options={options}
                placeholder={placeholder ? placeholder : ""}
                isDisabled={isDisabled}
                isClearable={isClearable}
                isSearchable={isSearchable}
                classNamePrefix={classNamePrefix}
                value={selectedOption}
                isLoading={isLoading}
                onChange={(val) => {
                  console.log("inside val", val);
                  setIsSelecting(true);
                  if (onChange) {
                    onChange(val);
                  }
                  field.onChange(val?.value);
                  // Reset the flag after state updates
                  setTimeout(() => setIsSelecting(false), 0);
                }}
                onInputChange={(inputValue, { action }) => {
                  // if (onInputChange) {
                  //   onInputChange(inputValue);
                  // }
                  // Only call onInputChange if it's an actual input change, not selection
                  if (action === "input-change" && onInputChange && !isSelecting) {
                    onInputChange(inputValue);
                  }
                }}
                onMenuClose={() => {
                  console.log("onMenuClose");
                }}
                onBlur={field.onBlur}
              />
              {errors && errors[name] && <p className="text-danger auth-msg danger">{errors[name]?.message as string}</p>}
            </>
          );
        }}
      />
    </div>
  );
};

export default ReactCommonSelect;
