import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinC<PERSON>umn,
  UpdateDateColumn,
  CreateDateColumn,
} from "typeorm";
import { JobsModel } from "./jobs";
import SkillsModel from "./skills";

/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
export enum SkillType {
  ROLE_SPECIFIC = "role_specific",
  CULTURE_SPECIFIC = "culture_specific",
  CAREER_BASED = "career_based",
}

@Entity("job_skills")
export default class JobSkillsModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "job_id" })
  jobId: number;

  @Column({ name: "skill_id" })
  skillId: number;

  @Column({
    type: "enum",
    enum: SkillType,
  })
  type: SkillType;

  @Column({ name: "is_additional", default: false })
  isAdditional: boolean;

  @ManyToOne(() => JobsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "job_id" })
  job: JobsModel;

  @ManyToOne(() => SkillsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "skill_id" })
  skill: SkillsModel;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}
