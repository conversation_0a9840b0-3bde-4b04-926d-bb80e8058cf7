import { RoundType } from "../../schema/s9-innerview/interview";

export interface ScheduleInterviewData {
  title: string;
  jobId: number;
  interviewerId: number;
  jobApplicationId: number;
  scheduleAt: number;
  startTime: number;
  endTime: number;
  roundType: RoundType;
  description?: string;
  fileUrlArray?: string;
  interviewId?: number;
}

export interface IGetJobList {
  orgId: number;
  searchString: string;
}

export interface IGetCandidateList extends IGetJobList {
  jobId: number;
}

export interface GetInterviewsData {
  jobId: string;
  applicationId: string;
  monthYear: string;
  interviewerId?: number;
}

export interface IGenerateInterviewSkillQuestions {
  jobId: number;
  jobApplicationId: number;
  interviewId: number;
  roundNumber: number;
}

export interface IUpdateInterviewSkillQuestion {
  interviewQuestionId: number;
  question: string;
}

export interface IGetInterviewSkillQuestions {
  jobApplicationId: number;
  interviewId?: number;
}

export interface IAddInterviewSkillQuestion {
  jobApplicationId: number;
  interviewId?: number;
  question: string;
  skillType: string;
  jobSkillId?: number;
}

export interface IUpdateInterviewAnswers {
  interviewId: number;
  skillMarked: number;
  skillType: string;
  jobSkillId?: number;
  skillId?: number;
  answers: Array<{ questionId: number; answer: string }>;
}

export interface IEndInterview {
  interviewId: number;
  behaviouralNotes?: string;
}
