import {
  DeleteObjectCommand,
  DeleteObjectsCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { CreateInvalidationCommand } from "@aws-sdk/client-cloudfront";
import envConfig from "../config/envConfig";
import awsConfig, { getSecretKeys } from "../config/awsConfig";

/**
 * getting pre url
 */
const CONFIG = envConfig();

export const gettingPreSignedUrl = async (
  filePath: string,
  fileFormat?: string
): Promise<string> => {
  try {
    // Use the properly configured S3 client from awsConfig
    const { s3Client } = awsConfig;

    // Create the command for the operation you want to presign
    const command = new PutObjectCommand({
      Key: `${filePath}`,
      Bucket: CONFIG.s3_bucket,
      ContentType: fileFormat,
    });

    // Generate presigned URL (expires in 10 minutes = 600 seconds)
    const signedUrl = await getSignedUrl(s3Client, command, {
      expiresIn: 600 * 600,
    });

    return signedUrl;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error("Error generating pre-signed URL:", error);
    throw error;
  }
};

export const deleteFileFromS3 = async (keys: string | string[]) => {
  const { s3Client, cloudFrontClient } = awsConfig;
  const secretKeys = await getSecretKeys();

  console.log(secretKeys, "===keys=====>>>>", keys);

  if (typeof keys === "string") {
    const params = {
      Bucket: CONFIG.s3_bucket,
      Key: keys,
    };

    try {
      const command = new DeleteObjectCommand(params);
      const isDelete = await s3Client.send(command);

      console.log("isDelete=====>>>>", isDelete);

      // Invalidate the cached content
      if (isDelete.DeleteMarker) {
        const invalidateParams = {
          DistributionId:
            secretKeys.s3_bucket_cloudfront_distribution_id_for_s9_innerview,
          InvalidationBatch: {
            Paths: {
              Quantity: 1,
              Items: [`/${keys}`],
            },
            CallerReference: `invalidate-${Date.now()}`, // For unique cache invalidation request
          },
        };

        console.log("invalidateParams=====>>>>", invalidateParams);

        const cloudfrontCommand = new CreateInvalidationCommand(
          invalidateParams
        );

        console.log("cloudfrontCommand=====>>>>", cloudfrontCommand);
        await cloudFrontClient.send(cloudfrontCommand);
      }
      return isDelete;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error deleting: ${error.message}`);
      return error;
    }
  } else {
    const params = {
      Bucket: CONFIG.s3_bucket,
      Delete: {
        Objects: keys.map((Key) => ({ Key })),
      },
    };

    try {
      const command = new DeleteObjectsCommand(params);
      const isDelete = await s3Client.send(command);

      console.log("isDelete Array=====>>>>", isDelete);

      // Invalidate the cached contents
      if (!isDelete.Errors || !isDelete.Errors.length) {
        const invalidateParams = {
          DistributionId:
            secretKeys.s3_bucket_cloudfront_distribution_id_for_s9_innerview,
          InvalidationBatch: {
            Paths: {
              Quantity: keys.length,
              Items: keys.map((Key) => `/${Key}`),
            },
            CallerReference: `invalidate-${Date.now()}`, // For unique cache invalidation request
          },
        };

        const cloudfrontCommand = new CreateInvalidationCommand(
          invalidateParams
        );
        await cloudFrontClient.send(cloudfrontCommand);
      }

      return isDelete;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(`Error deleting: ${error.message}`);
      return error;
    }
  }
};
