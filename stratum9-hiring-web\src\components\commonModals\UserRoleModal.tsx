"use client";
import React, { FC, useState, useEffect } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import { useForm, useWatch } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { addUserRole, updateUserRole, deleteUserRole } from "@/services/roleService";
import { roleValidationSchema } from "@/utils/validationSchema";
import { RoleFormData, RoleModalProps } from "@/interfaces/roleInterface";
import { UserRoleForm } from "@/interfaces/employeeInterface";
import Loader from "../loader/Loader";
import { ROLE_ALTER_MODE } from "../views/accessManagement/UserRoles";
import { useTranslations } from "next-intl";

const UserRoleModal: FC<RoleModalProps> = ({ onClickCancel, onSubmitSuccess, disabled, role, mode }) => {
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isNameChanged, setIsNameChanged] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<RoleFormData>({
    defaultValues: {
      name: role?.name || "",
    },
    resolver: yupResolver(roleValidationSchema(t)),
    mode: "onChange", // Add onChange validation mode
  });

  // Watch for changes in the name field
  const currentName = useWatch({
    control,
    name: "name",
    defaultValue: role?.name || "",
  });

  // Update isNameChanged when the name changes
  useEffect(() => {
    if (mode === ROLE_ALTER_MODE.EDIT && role) {
      const trimmedCurrentName = currentName.trim();
      const trimmedOriginalName = role.name.trim();
      setIsNameChanged(trimmedCurrentName !== trimmedOriginalName);
    }
  }, [currentName, role, mode]);

  const onSubmit = async (data: RoleFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      const requestData: UserRoleForm = {
        name: data.name,
      };
      let response;
      try {
        if (mode === ROLE_ALTER_MODE.ADD) {
          response = await addUserRole(requestData);
        } else if (mode === ROLE_ALTER_MODE.EDIT && role) {
          response = await updateUserRole(role.id, requestData);
        } else if (mode === ROLE_ALTER_MODE.DELETE && role) {
          response = await deleteUserRole(role.id);
        } else {
          throw new Error(t("unexpected_error"));
        }

        const result = response.data;

        if (result && result.success) {
          // Call onSubmitSuccess with the success message
          const actionType = mode === ROLE_ALTER_MODE.ADD ? t("added") : mode === ROLE_ALTER_MODE.EDIT ? t("updated") : t("deleted");
          const successMessage = t("role_action_success", { actionType: actionType });
          onSubmitSuccess(successMessage);
          onClickCancel();
        } else {
          const errorMessage = t(result.message || "failed_role_operation");
          setSubmitError(errorMessage);
        }
      } catch (error) {
        console.error(error);
        const apiError = response?.error;
        const errorMessage = apiError?.status === 401 ? t("authentication_error") : apiError?.message || t("failed_role_operation");

        setSubmitError(errorMessage);
      }
    } catch (error) {
      console.error(error);
      setSubmitError(t("unexpected_error"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case ROLE_ALTER_MODE.ADD:
        return t("add_role");
      case ROLE_ALTER_MODE.EDIT:
        return t("edit_role");
      case ROLE_ALTER_MODE.DELETE:
        return t("delete_role");
      default:
        return t("role");
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>{getModalTitle()}</h2>

            <Button className="modal-close-btn" onClick={onClickCancel} disabled={isSubmitting}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            {mode === ROLE_ALTER_MODE.DELETE ? (
              <div>
                <p className="text-center mb-4">{t("confirm_delete_role", { roleName: role?.name || "" })}</p>

                {submitError && (
                  <div className="alert alert-danger mb-3" role="alert">
                    {submitError}
                  </div>
                )}

                <div className="button-align mt-4">
                  <Button type="button" className="danger-btn rounded-md w-100" onClick={handleSubmit(onSubmit)} disabled={isSubmitting || disabled}>
                    <div className="d-flex align-items-center justify-content-center">
                      {isSubmitting && <Loader />}
                      <span className={isSubmitting ? "ms-2" : ""}>{t("delete_role")}</span>
                    </div>
                  </Button>
                  <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting || disabled}>
                    {t("cancel")}
                  </Button>
                </div>
              </div>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)}>
                <InputWrapper className="mb-4">
                  <InputWrapper.Label htmlFor="name" required className="fw-bold">
                    {t("role_name")}
                  </InputWrapper.Label>
                  <Textbox
                    className="form-control"
                    control={control}
                    name="name"
                    type="text"
                    placeholder={t("enter_role_name")}
                    disabled={isSubmitting || disabled}
                  />
                  <InputWrapper.Error message={errors?.name?.message || ""} />
                </InputWrapper>

                {submitError && (
                  <div className="alert alert-danger mb-3" role="alert">
                    {submitError}
                  </div>
                )}

                <div className="button-align mt-4">
                  <Button
                    type="submit"
                    className={`primary-btn rounded-md w-100 ${isSubmitting || disabled || (mode === ROLE_ALTER_MODE.EDIT && !isNameChanged) || !isValid ? "truly-disabled" : ""}`}
                    disabled={isSubmitting || disabled || (mode === ROLE_ALTER_MODE.EDIT && !isNameChanged) || !isValid}
                    title={mode === ROLE_ALTER_MODE.EDIT && !isNameChanged ? t("change_role_name_hint") : ""}
                  >
                    <div className="d-flex align-items-center justify-content-center">
                      {isSubmitting && <Loader />}
                      <span className={isSubmitting ? "ms-2" : ""}>{getModalTitle()}</span>
                    </div>
                  </Button>
                  <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onClickCancel} disabled={isSubmitting || disabled}>
                    {t("cancel")}
                  </Button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserRoleModal;
