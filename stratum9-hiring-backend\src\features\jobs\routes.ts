import express from "express";

import HandleErrors from "../../middleware/handleError";
import { sanitizeBody } from "../../middleware/sanitize";
import { schemaValidation } from "../../middleware/validateSchema";
import isAuthorized, {
  authorizedForCreateOrEditJobPost,
  authorizedForArchiveRestoreJobPosts,
} from "../../middleware/isAuthorized";

import generateJobSkillsSchema from "./validation";
import {
  generateJobSkills,
  getJobDescUploadUrl,
  getSkills,
  generateJobRequirement,
  saveJobDetails,
  getAllJobsMeta,
  updateJob,
  getDashboardCounts,
  getJobHtmlDescription,
  updateJobDescription,
  generatePdf,
} from "./controller";

import { PERMISSION, ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import upload from "../../middleware/upload";

// Configure multer for memory storage (buffer)

/**
 * @swagger
 * tags:
 *   name: Jobs
 *   description: Job management and related operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     JobSkillsRequest:
 *       type: object
 *       required:
 *         - jobDescription
 *       properties:
 *         jobDescription:
 *           type: string
 *           description: The job description text used to generate relevant skills
 *     JobSkillsResponse:
 *       type: object
 *       properties:
 *         skills:
 *           type: array
 *           items:
 *             type: string
 *           description: List of skills relevant to the job description
 *         message:
 *           type: string
 *           description: Success or error message
 */

const jobRoutes = express.Router();

/**
 * @swagger
 * /api/jobs/generate-skills:
 *   post:
 *     summary: Generate relevant skills based on job description
 *     description: Uses AI to analyze a job description and return relevant skills
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/JobSkillsRequest'
 *     responses:
 *       200:
 *         description: Skills successfully generated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/JobSkillsResponse'
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized access
 *       500:
 *         description: Server error occurred
 */
jobRoutes.post(
  ROUTES.JOBS.GENERATE_SKILLS,
  auth,
  isAuthorized(PERMISSION.CREATE_OR_EDIT_JOB_POST),
  authorizedForCreateOrEditJobPost,
  sanitizeBody(),
  schemaValidation(generateJobSkillsSchema),
  HandleErrors(generateJobSkills)
);

/**
 * @swagger
 * /api/jobs/upload-url:
 *   post:
 *     summary: Upload job description PDF, extract data and pre-fill form fields
 *     description: Uploads a PDF job description, parses its content, extracts structured job information using GPT, and returns form field values
 *     tags: [Jobs]
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: file
 *         type: file
 *         required: true
 *         description: Job description PDF file to upload and process
 *       - in: formData
 *         name: user_id
 *         type: string
 *         description: User ID (defaults to "1" if not provided)
 *       - in: formData
 *         name: org_id
 *         type: string
 *         description: Organization ID (defaults to "1" if not provided)
 *     responses:
 *       200:
 *         description: PDF successfully processed and form fields extracted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates if the operation was successful
 *                 data:
 *                   type: object
 *                   properties:
 *                     uploadUrl:
 *                       type: string
 *                       description: Pre-signed URL for uploading the file to storage
 *                     jdLink:
 *                       type: string
 *                       description: Path/link to the uploaded file
 *                     formFields:
 *                       type: object
 *                       description: Extracted form field values from the PDF content
 *                       properties:
 *                         job_title:
 *                           type: string
 *                         company_name:
 *                           type: string
 *                         job_location:
 *                           type: string
 *                         job_type:
 *                           type: string
 *                         job_description:
 *                           type: string
 *                         responsibilities:
 *                           type: array
 *                           items:
 *                             type: string
 *                         requirements:
 *                           type: array
 *                           items:
 *                             type: string
 *                         skills_required:
 *                           type: array
 *                           items:
 *                             type: string
 *                     pdfData:
 *                       type: object
 *                       description: Parsed PDF data
 *       400:
 *         description: No PDF file uploaded or invalid request
 *       422:
 *         description: Could not process the PDF file
 *       500:
 *         description: Server error occurred
 */
jobRoutes.post(
  ROUTES.JOBS.UPLOAD_URL,
  upload.single("file"),
  auth,
  isAuthorized(PERMISSION.CREATE_OR_EDIT_JOB_POST),
  authorizedForCreateOrEditJobPost,
  HandleErrors(getJobDescUploadUrl)
);

/**
 * @swagger
 * /api/jobs/get-all-skills:
 *   get:
 *     summary: Get all available skills from the database
 *     description: Retrieves all skills data categorized by type from the database
 *     tags: [Jobs]
 *     responses:
 *       200:
 *         description: Skills data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   description: Indicates if the operation was successful
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 data:
 *                   type: array
 *                   description: Array of skill types with their items
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                         description: Skill category type
 *                       items:
 *                         type: array
 *                         description: Skills in this category
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               description: Skill ID
 *                             title:
 *                               type: string
 *                               description: Skill title
 *                             short_description:
 *                               type: string
 *                               description: Brief description of the skill
 *                             description:
 *                               type: string
 *                               description: Full description of the skill
 *                             value_points:
 *                               type: string
 *                               description: Value points associated with the skill
 *       500:
 *         description: Server error occurred
 */
jobRoutes.get(
  ROUTES.JOBS.GET_ALL_SKILLS,
  auth,
  authorizedForCreateOrEditJobPost,
  HandleErrors(getSkills)
);

/**
 * @swagger
 * /api/jobs/generate-job-requirement:
 *   post:
 *     summary: Generate job requirement
 *     description: Processes job details and skills to generate a full job requirement
 *     tags: [Jobs]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Job requirement generated successfully
 *       500:
 *         description: Server error occurred
 */
jobRoutes.post(
  ROUTES.JOBS.GENERATE_JOB_REQUIREMENT,
  auth,
  sanitizeBody(),
  HandleErrors(generateJobRequirement)
);

/**
 * @swagger
 * /api/jobs/save-job-details:
 *   post:
 *     summary: Save job details
 *     description: Saves the job details to the database
 *     tags: [Jobs]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Job details saved successfully
 *       500:
 *         description: Server error occurred
 */
jobRoutes.post(
  ROUTES.JOBS.SAVE_JOB_DETAILS,
  auth,
  isAuthorized(PERMISSION.CREATE_OR_EDIT_JOB_POST),
  authorizedForCreateOrEditJobPost,
  sanitizeBody(),
  HandleErrors(saveJobDetails)
);

/**
 * @swagger
 * /api/jobs/get-all-jobs-meta:
 *   get:
 *     summary: Get all job metadata
 *     description: Returns job id, title, and created timestamp
 *     tags: [Jobs]
 *     responses:
 *       200:
 *         description: Job metadata retrieved successfully
 *       500:
 *         description: Server error occurred
 */
jobRoutes.get(ROUTES.JOBS.GET_JOBS_META, auth, HandleErrors(getAllJobsMeta)); // ✅ NEW ROUTE

/**
 * @swagger
 * /api/jobs/deactivate/{id}:
 *   put:
 *     summary: Deactivate a job by ID
 *     description: Sets the isActive field of the specified job to false (archive)
 *     tags: [Jobs]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the job to deactivate
 *     responses:
 *       200:
 *         description: Job successfully deactivated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Invalid job ID
 *       404:
 *         description: Job not found
 *       500:
 *         description: Server error occurred
 */
jobRoutes.put(
  ROUTES.JOBS.UPDATE_JOB,
  auth,
  authorizedForArchiveRestoreJobPosts,
  HandleErrors(updateJob)
);
// ✅ Added deactivate job API

/**
 * @swagger
 * /api/jobs/dashboard-counts:
 *   get:
 *     summary: Get dashboard counts for jobs
 *     description: Returns counts of total and active jobs for the specified organization and user
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organizationId
 *         schema:
 *           type: string
 *         required: true
 *         description: ID of the organization
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: ID of the user
 *     responses:
 *       200:
 *         description: Successfully retrieved dashboard counts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Dashboard counts fetched successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalJobs:
 *                       type: number
 *                       example: 10
 *                       description: Total number of jobs for the user in the organization
 *                     activeJobs:
 *                       type: number
 *                       example: 5
 *                       description: Number of active jobs for the user in the organization
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: organizationId and userId are required query parameters
 *                 code:
 *                   type: number
 *                   example: 400
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Failed to fetch dashboard counts
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.get(
  ROUTES.JOBS.DASHBOARD_COUNTS,
  auth,
  HandleErrors(getDashboardCounts)
);

/**
 * @swagger
 * /api/jobs/get-job-html-description/{id}:
 *   get:
 *     summary: Get job HTML description by job ID
 *     description: Returns the HTML description of the specified job
 *     tags: [Jobs]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the job to fetch HTML description for
 *     responses:
 *       200:
 *         description: Successfully retrieved job HTML description
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Job HTML description retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: number
 *                       example: 123
 *                     title:
 *                       type: string
 *                       example: "Job Title"
 *                     htmlDescription:
 *                       type: string
 *                       example: "Job HTML description"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Invalid job ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid job ID
 *                 code:
 *                   type: number
 *                   example: 400
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Job with id 999 not found
 *                 code:
 *                   type: number
 *                   example: 404
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Failed to retrieve job HTML description
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.get(
  ROUTES.JOBS.GET_JOB_HTML_DESCRIPTION,
  auth,
  HandleErrors(getJobHtmlDescription)
);
export default jobRoutes;

/**
 * @swagger
 * /api/jobs/update-job-description:
 *   put:
 *     summary: Update job description
 *     description: Updates the HTML description of a job
 *     tags: [Jobs]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Numeric ID of the job to update
 *               finalJobDescriptionHtml:
 *                 type: string
 *                 description: HTML description of the job
 *             required:
 *               - id
 *               - finalJobDescriptionHtml
 *     responses:
 *       200:
 *         description: Job description updated successfully
 *       400:
 *         description: Invalid job ID
 *       404:
 *         description: Job not found
 *       500:
 *         description: Server error occurred
 */
jobRoutes.put(
  ROUTES.JOBS.UPDATE_JOB_DESCRIPTION,
  auth,
  HandleErrors(updateJobDescription)
);
/**
 * @swagger
 * /api/jobs/generate-pdf:
 *   post:
 *     summary: Generate PDF from job description
 *     description: Generates a PDF from the provided job description
 *     tags: [Jobs]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *                 description: Numeric ID of the job to generate PDF for
 *             required:
 *               - id
 *     responses:
 *       200:
 *         description: PDF generated successfully
 *       400:
 *         description: Invalid job ID
 *       404:
 *         description: Job not found
 *       500:
 *         description: Server error occurred
 */
jobRoutes.post(ROUTES.JOBS.GENERATE_PDF, auth, HandleErrors(generatePdf));
