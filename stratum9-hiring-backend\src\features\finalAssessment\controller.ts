import { Request, Response } from "express";
import * as Sentry from "@sentry/node";
import FinalAssessmentServices from "./services";
// import { createFinalAssessmentValidation } from "./validations";

/**
 * Create a new final assessment
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const createFinalAssessment = async (req: Request, res: Response) => {
  try {
    const data = await FinalAssessmentServices.createFinalAssessment({
      ...req.body,
      orgId: req.orgId,
    });

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get final assessment questions grouped by skill type
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getFinalAssessmentQuestion = async (
  req: Request,
  res: Response
) => {
  try {
    const { finalAssessmentId, jobId, jobApplicationId } = req.query;

    const data = await FinalAssessmentServices.getFinalAssessmentQuestion({
      finalAssessmentId: +finalAssessmentId,
      jobId: +jobId,
      jobApplicationId: +jobApplicationId,
      orgId: req.orgId,
    });

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add a manual question to a final assessment
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const addManualFinalAssessmentQuestion = async (
  req: Request,
  res: Response
) => {
  try {
    const result =
      await FinalAssessmentServices.addManualFinalAssessmentQuestion({
        ...req.body,
        orgId: req.orgId,
      });

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Share assessment with candidate via email
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const shareAssessment = async (req: Request, res: Response) => {
  try {
    const { finalAssessmentId, assessmentLink, jobApplicationId } = req.body;
    const data = await FinalAssessmentServices.shareAssessmentToCandidate(
      +finalAssessmentId,
      assessmentLink,
      +jobApplicationId,
      req.orgId
    );

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get final assessment by encrypted ID for candidate view
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getFinalAssessmentByCandidate = async (
  req: Request,
  res: Response
) => {
  try {
    const { finalAssessmentId } = req.query;
    console.log("finalAssessmentId", finalAssessmentId);

    const data =
      await FinalAssessmentServices.getFinalAssessmentByCandidate(
        +finalAssessmentId
      );

    return res.status(200).json({
      success: data.success,
      message: data.message,
      data: data.data,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Submit candidate answers for an assessment
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */

/**
 * Get assessment status based on isShare and isSubmit flags
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getAssessmentStatus = async (req: Request, res: Response) => {
  try {
    const { jobId, jobApplicationId } = req.body;

    const data = await FinalAssessmentServices.getAssessmentStatus(
      +jobId,
      +jobApplicationId
    );

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const submitAssessment = async (req: Request, res: Response) => {
  try {
    const { finalAssessmentId, candidateEmail, answers } = req.body;

    const data = await FinalAssessmentServices.submitAnswerByCandidate(
      finalAssessmentId,
      candidateEmail,
      answers
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Verify if candidate email exists in the system
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const verifyCandidateEmail = async (req: Request, res: Response) => {
  try {
    const { email, token } = req.body;

    const data = await FinalAssessmentServices.verifyCandidateEmail(
      email,
      token
    );

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Generate a JWT token for a final assessment
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const generateAssessmentToken = async (req: Request, res: Response) => {
  try {
    const { finalAssessmentId } = req.body;
    const data =
      await FinalAssessmentServices.generateAssessmentToken(+finalAssessmentId);

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    Sentry.captureException(error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};
