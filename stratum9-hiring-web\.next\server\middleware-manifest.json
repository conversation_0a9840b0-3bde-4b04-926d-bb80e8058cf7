{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c13982b5._.js", "server/edge/chunks/[root of the server]__9de05221._.js", "server/edge/chunks/edge-wrapper_27c01396.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "7b148iY6kC2GKGoIUzL1nh7sS1kq9eLEAekyOZnGXL4=", "__NEXT_PREVIEW_MODE_ID": "a7a90c5e2f8c74145ff832416907c8f4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "372a8d98a87eed1d5ea6401a33efd171bb71314a2b9a991e5c2e02cca34d8f25", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "20fd29e3e03b89652437193f2bf41eab79eec34f661e97ff0abea42e63dd22c2"}}}, "sortedMiddleware": ["/"], "functions": {}}