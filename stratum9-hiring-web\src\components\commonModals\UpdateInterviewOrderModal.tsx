"use client";
import React, { FC, useState, useMemo } from "react";
import Button from "../formElements/Button";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Loader from "../loader/Loader";
import InputWrapper from "../formElements/InputWrapper";
import Textbox from "../formElements/Textbox";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useTranslations } from "next-intl";
import { EmployeeInterface } from "@/interfaces/employeeInterface";
import styles from "../../styles/accessManagement.module.scss";
import { updateInterviewOrderValidationSchema } from "@/validations/employeeManagementValidations";

interface IProps {
  employeeName: string;
  currentOrder: number;
  onConfirm: (newOrder: number) => void;
  onCancel: () => void;
  isLoading?: boolean;
  employees?: EmployeeInterface[];
}

const UpdateInterviewOrderModal: FC<IProps> = ({ employeeName, currentOrder, onConfirm, onCancel, isLoading = false, employees = [] }) => {
  const t = useTranslations();
  const [submitError, setSubmitError] = useState<string | null>(null);

  const orderOptions = useMemo(() => {
    const existingOrders = employees
      .map((emp) => emp.interviewOrder)
      .filter((order) => order !== undefined && order !== null)
      .sort((a, b) => a - b);

    // Create a set of unique orders from existing only
    const uniqueOrders = new Set(existingOrders);

    // Convert to array and sort
    return Array.from(uniqueOrders).sort((a, b) => a - b);
  }, [employees]);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
  } = useForm({
    defaultValues: {
      interviewOrder: currentOrder,
    },
    resolver: yupResolver(updateInterviewOrderValidationSchema(t)),
    mode: "onChange",
  });

  const onSubmit = (data: { interviewOrder: number }) => {
    try {
      onConfirm(data.interviewOrder);
    } catch (error) {
      console.error(error);
      setSubmitError(t("unexpected_error"));
    }
  };

  return (
    <div className="modal theme-modal show-modal">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header justify-content-center">
            <h2>{t("update_interview_order")}</h2>
            <Button className="modal-close-btn" onClick={onCancel} disabled={isLoading}>
              <ModalCloseIcon />
            </Button>
          </div>
          <div className="modal-body">
            <form onSubmit={handleSubmit(onSubmit)}>
              <p className="text-center mb-3">
                {t("updating_order_for")} <strong>"{employeeName}"</strong>
              </p>

              <InputWrapper className="mb-4">
                <InputWrapper.Label htmlFor="interviewOrder" required className="fw-bold">
                  {t("interview_order")}
                </InputWrapper.Label>

                <div className="d-flex gap-2 w-100">
                  {/* Dropdown selector for existing orders */}
                  <select
                    className={`form-select ${styles.role_select || ""}`}
                    style={{ fontSize: "18px", padding: "12px", height: "auto", minWidth: "120px" }}
                    value={watch("interviewOrder")}
                    onChange={(e) => {
                      // Use setValue from react-hook-form instead of DOM manipulation
                      const newValue = parseInt(e.target.value, 10);
                      if (!isNaN(newValue)) {
                        // Set the value in the form using setValue
                        setValue("interviewOrder", newValue, { shouldValidate: true });
                      }
                    }}
                    disabled={isLoading}
                  >
                    {orderOptions.map((order) => (
                      <option key={order} value={order}>
                        {order}
                      </option>
                    ))}
                  </select>

                  {/* Hidden input field for form validation */}
                  <div style={{ display: "none" }}>
                    <Textbox className="form-control" control={control} name="interviewOrder" type="number" disabled={isLoading} />
                  </div>
                </div>

                <InputWrapper.Error message={errors?.interviewOrder?.message || ""} />
              </InputWrapper>

              {submitError && (
                <div className="alert alert-danger mb-3" role="alert">
                  {submitError}
                </div>
              )}

              <div className="button-align mt-4">
                <Button
                  type="submit"
                  className={`primary-btn rounded-md w-100 ${isLoading || !isValid ? "truly-disabled" : ""}`}
                  disabled={isLoading || !isValid}
                >
                  <div className="d-flex align-items-center justify-content-center">
                    {isLoading && <Loader />}
                    <span className={isLoading ? "ms-2" : ""}>{t("update_order")}</span>
                  </div>
                </Button>
                <Button type="button" className="dark-outline-btn rounded-md w-100" onClick={onCancel} disabled={isLoading}>
                  {t("cancel")}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpdateInterviewOrderModal;
