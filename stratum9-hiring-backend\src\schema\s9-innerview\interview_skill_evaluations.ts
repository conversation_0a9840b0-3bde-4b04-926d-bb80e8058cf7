/* eslint-disable no-unused-vars */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

@Entity("interview_skill_evaluations")
class InterviewSkillEvaluationModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "skill_id", nullable: false })
  skillId: number;

  @Column({ name: "job_skill_id", nullable: false })
  jobSkillId: number;

  @Column({ name: "interview_id", nullable: false })
  interviewId: number;

  @Column({ name: "job_application_id", nullable: false })
  jobApplicationId: number;

  @Column({ name: "interviewer_id", nullable: false })
  interviewerId: number;

  @Column({ name: "locked", default: false })
  locked: boolean;

  @Column({ name: "skill_marks", nullable: false })
  skillMarks: number;

  @Column({ name: "strengths", type: "json" })
  strengths: object;

  @Column({ name: "potentials_gaps", type: "json" })
  potentialsGaps: object;

  @Column({ name: "probability_of_success_in_this_skill", type: "json" })
  probabilityOfSuccessInThisSkill: object;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_at",
    nullable: false,
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
    nullable: false,
  })
  updatedTs: Date;
}

export default InterviewSkillEvaluationModel;
