import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useTranslations } from "next-intl";

import { IGetInterviewSkillQuestionsResponse } from "@/interfaces/interviewInterfaces";

// Define interfaces for our progress tracking
interface ProgressState {
  career: {
    totalSkills: number;
    completedSkills: number;
  };
  culture: {
    totalSkills: number;
    totalQuestions: number;
    completedQuestions: number;
    skillProgress: Record<string, number>; // skill name -> completed questions count
  };
  role: {
    totalSkills: number;
    totalQuestions: number;
    completedQuestions: number;
    skillProgress: Record<string, number>; // skill name -> completed questions count
  };
}

const ProgressTracker: React.FC<{ isRecording: boolean }> = ({ isRecording }) => {
  // Initialize progress state
  const t = useTranslations();
  const [progressState, setProgressState] = useState<ProgressState>({
    career: {
      totalSkills: 0,
      completedSkills: 0,
    },
    culture: {
      totalSkills: 0,
      totalQuestions: 0,
      completedQuestions: 0,
      skillProgress: {},
    },
    role: {
      totalSkills: 0,
      totalQuestions: 0,
      completedQuestions: 0,
      skillProgress: {},
    },
  });

  // Add state for tracking recording time
  const [elapsedTime, setElapsedTime] = useState<number>(0);

  // Get interview data from Redux store
  const interviewData = useSelector((state: { interview: IGetInterviewSkillQuestionsResponse }) => state.interview);

  // Calculate progress percentages
  const careerProgress = progressState.career.totalSkills > 0 ? (progressState.career.completedSkills / progressState.career.totalSkills) * 100 : 0;

  const cultureProgress =
    progressState.culture.totalQuestions > 0 ? (progressState.culture.completedQuestions / progressState.culture.totalQuestions) * 100 : 0;

  const roleProgress = progressState.role.totalQuestions > 0 ? (progressState.role.completedQuestions / progressState.role.totalQuestions) * 100 : 0;

  // Calculate overall progress (average of the three sections)
  const overallProgress = (careerProgress + cultureProgress + roleProgress) / 3;

  // Update progress state based on interview data
  useEffect(() => {
    if (!interviewData) return;

    const newProgressState: ProgressState = {
      career: {
        totalSkills: 0,
        completedSkills: 0,
      },
      culture: {
        totalSkills: 0,
        totalQuestions: 0,
        completedQuestions: 0,
        skillProgress: {},
      },
      role: {
        totalSkills: 0,
        totalQuestions: 0,
        completedQuestions: 0,
        skillProgress: {},
      },
    };

    // Process career-based questions
    if (interviewData.careerBasedQuestions) {
      const careerSkills = Object.keys(interviewData.careerBasedQuestions);
      newProgressState.career.totalSkills = careerSkills.length;

      // Count completed skills (a skill is completed if it has at least one answered question)
      newProgressState.career.completedSkills = careerSkills.filter(() => {
        const questions = interviewData.careerBasedQuestions.questions || [];
        return questions.some((q) => q.answer && q.answer.trim() !== "");
      }).length;
    }

    // Process culture-based questions
    if (interviewData.cultureSpecificQuestions) {
      const cultureSkills = Object.keys(interviewData.cultureSpecificQuestions);
      newProgressState.culture.totalSkills = cultureSkills.length;

      let completedQuestions = 0;
      let totalQuestions = 0;

      cultureSkills.forEach((skill) => {
        const questions = interviewData.cultureSpecificQuestions[skill].questions || [];
        totalQuestions += questions.length;

        const skillCompletedQuestions = questions.filter((q) => q.answer && q.answer.trim() !== "").length;
        completedQuestions += skillCompletedQuestions;

        newProgressState.culture.skillProgress[skill] = skillCompletedQuestions;
      });

      newProgressState.culture.totalQuestions = totalQuestions;
      newProgressState.culture.completedQuestions = completedQuestions;
    }

    // Process role-based questions
    if (interviewData.roleSpecificQuestions) {
      const roleSkills = Object.keys(interviewData.roleSpecificQuestions);
      newProgressState.role.totalSkills = roleSkills.length;

      let completedQuestions = 0;
      let totalQuestions = 0;

      roleSkills.forEach((skill) => {
        const questions = interviewData.roleSpecificQuestions[skill].questions || [];
        totalQuestions += questions.length;

        const skillCompletedQuestions = questions.filter((q) => q.answer && q.answer.trim() !== "").length;
        completedQuestions += skillCompletedQuestions;

        newProgressState.role.skillProgress[skill] = skillCompletedQuestions;
      });

      newProgressState.role.totalQuestions = totalQuestions;
      newProgressState.role.completedQuestions = completedQuestions;
    }

    // Update state and save to localStorage
    setProgressState(newProgressState);
    localStorage.setItem("interviewProgress", JSON.stringify(newProgressState));
  }, [interviewData]);

  // Add timer functionality when recording starts/stops
  useEffect(() => {
    let timerId: NodeJS.Timeout | null = null;

    if (isRecording) {
      // Start the timer when recording begins
      timerId = setInterval(() => {
        setElapsedTime((prevTime) => prevTime + 1);
      }, 1000);
    } else {
      // Reset the timer when recording stops
      setElapsedTime(0);
    }

    // Cleanup the interval when component unmounts or isRecording changes
    return () => {
      if (timerId) clearInterval(timerId);
    };
  }, [isRecording]);

  // Format the elapsed time as HH:MM:SS
  const formatTime = (timeInSeconds: number): string => {
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = timeInSeconds % 60;

    return [hours, minutes, seconds].map((val) => val.toString().padStart(2, "0")).join(":");
  };

  return (
    <div className="progress-container">
      <div className="d-flex justify-content-between align-items-center">
        <p className="time">{formatTime(elapsedTime)}</p>
        {isRecording ? <p className="status">{t("recording_in_progress")}</p> : null}
      </div>
      <div className="progress-tracker">
        <div className="bar-container">
          <div className="bar">
            <div className="progress" style={{ width: `${overallProgress}%` }} />
            <div className="marker active" style={{ left: "0%", opacity: 0 }} />
            <div className="marker active" style={{ left: "33.3333%" }} />
            <div className="marker active" style={{ left: "66.6667%" }} />
            <div className="marker" style={{ left: "100%", opacity: 0 }} />
          </div>
          <div className="labels">
            <div className="label" style={{ left: "0%" }}>
              {t("career_based") + t("interview_")}
            </div>
            <div className="label" style={{ left: "33.3333%" }}>
              {t("role_based") + t("interview_")}
            </div>
            <div className="label" style={{ left: "66.6667%" }}>
              {t("culture_based") + t("interview_")}
            </div>
            <div className="label" style={{ left: "100%" }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressTracker;
