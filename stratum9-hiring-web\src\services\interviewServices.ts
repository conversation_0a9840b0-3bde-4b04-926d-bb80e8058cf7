import * as http from "@/utils/http";
import endpoint from "@/constants/endpoint";
import { ApiResponse } from "@/interfaces/commonInterfaces";
import {
  IAddInterviewSkillQuestion,
  IGetCandidateListResponse,
  IGetInterviewersResponse,
  IGetInterviews,
  IGetInterviewSkillQuestions,
  IGetInterviewSkillQuestionsResponse,
  IGetInterviewsResponse,
  IGetJobListResponse,
  IInterviewStaticInformation,
  IScheduleInterview,
  IUpcomingOrPastInterview,
  IUpdateInterviewAnswers,
  IUpdateInterviewSkillQuestion,
  IUpdateScheduleInterview,
} from "@/interfaces/interviewInterfaces";

export const updateOrScheduleInterview = (data: IScheduleInterview | IUpdateScheduleInterview): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.UPDATE_OR_SCHEDULE_INTERVIEW, data);
};

export const getInterviews = (data: IGetInterviews): Promise<ApiResponse<IGetInterviewsResponse[]>> => {
  return http.get(endpoint.interview.GET_INTERVIEWS, data);
};

export const getInterviewers = (searchString: string, jobId: string): Promise<ApiResponse<IGetInterviewersResponse[]>> => {
  return http.get(endpoint.interview.GET_INTERVIEWERS, { searchString, jobId });
};

export const upcomigOrPastInterview = (params: {
  isPast: boolean;
  limit?: number;
  offset?: number;
  searchStr?: string;
}): Promise<ApiResponse<IUpcomingOrPastInterview[]>> => {
  return http.get(endpoint.interview.GET_UPCOMING_OR_PAST_INTERVIEW, { ...params });
};

export const getMyInterviews = (monthYear: string): Promise<ApiResponse<IGetInterviewsResponse[]>> => {
  return http.get(endpoint.interview.GET_MY_INTERVIEWS, { monthYear });
};

export const getInterviewSkillQuestions = (data: IGetInterviewSkillQuestions): Promise<ApiResponse<IGetInterviewSkillQuestionsResponse>> => {
  return http.get(endpoint.interview.GET_INTERVIEW_SKILL_QUESTIONS, data);
};

export const updateInterviewSkillQuestion = (data: IUpdateInterviewSkillQuestion): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.UPDATE_INTERVIEW_SKILL_QUESTION, data);
};

export const addInterviewSkillQuestion = (data: IAddInterviewSkillQuestion): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.ADD_INTERVIEW_SKILL_QUESTION, data);
};

export const getJobList = (searchString: string): Promise<ApiResponse<IGetJobListResponse[]>> => {
  return http.get(endpoint.interview.GET_JOB_LIST, { searchString });
};

export const getCandidateList = (data: { searchString: string; jobId: string }): Promise<ApiResponse<IGetCandidateListResponse[]>> => {
  return http.get(endpoint.interview.GET_CANDIDATE_LIST, data);
};

export const updateInterviewAnswers = (data: IUpdateInterviewAnswers): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.UPDATE_INTERVIEW_ANSWERS, data);
};

export const endInterview = (data: { interviewId: number; behaviouralNotes: string }): Promise<ApiResponse<null>> => {
  return http.post(endpoint.interview.END_INTERVIEW, data);
};

export const conductInterviewStaticInformation = (): Promise<ApiResponse<IInterviewStaticInformation>> => {
  return http.get(endpoint.interview.CONDUCT_INTERVIEW_STATIC_INFORMATION);
};
