import pdf from "pdf-parse";
import http from "http";
import { Server, Socket } from "socket.io";
import { createClient, LiveTranscriptionEvents } from "@deepgram/sdk";
import * as jwt from "jsonwebtoken";
import { Repository } from "typeorm";
import dbConnection from "../db/dbConnection";
import {
  ATTEMPT,
  PDF_PARSING_MAX_ATTEMPTS,
  PASSWORD_REGEX,
  REDIS_KEYS,
  SOCKET_ROUTES,
} from "./constants";
import { getSecretKeys } from "../config/awsConfig";
import Cache from "../db/cache";

export const generateRandomPassword = () => {
  const lower = "abcdefghijklmnopqrstuvwxyz";
  const upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const digits = "0123456789";
  const symbols = "!@#$%^&*()-_=+[]{}|;:,.<>?";
  const all = lower + upper + digits + symbols;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  // Start with one from each required group
  const password = [
    getRandom(lower),
    getRandom(upper),
    getRandom(digits),
    getRandom(symbols),
  ];

  // Fill the rest up to a random length between 8–16
  const targetLength = Math.floor(Math.random() * 9) + 8;
  while (password.length < targetLength) {
    password.push(getRandom(all));
  }

  // Shuffle and return
  const final = password.sort(() => 0.5 - Math.random()).join("");

  return PASSWORD_REGEX.test(final) ? final : generateRandomPassword();
};

export async function getRepositories<T extends Record<string, any>>(
  modelMap: T
): Promise<{ [K in keyof T]: Repository<InstanceType<T[K]>> }> {
  const entries = await Promise.all(
    Object.entries(modelMap).map(async ([key, model]) => [
      key,
      await dbConnection.getS9InnerViewDatabaseRepository(model),
    ])
  );
  return Object.fromEntries(entries) as {
    [K in keyof T]: Repository<InstanceType<T[K]>>;
  };
}

// Retry Mechanism for PDF parsing
export const parsePdfWithRetries = async (
  buffer: Buffer,
  maxAttempts = PDF_PARSING_MAX_ATTEMPTS,
  attempt = ATTEMPT
): Promise<{ text: string } | null> => {
  try {
    const pdfParsed = await pdf(buffer);
    if (pdfParsed.text.trim() === "") {
      // Succesret
      return null;
    }
    return pdfParsed;
  } catch (error) {
    console.log("Error parsing PDF =========>", error);
  }
  if (attempt + 1 < maxAttempts) {
    return parsePdfWithRetries(buffer, maxAttempts, attempt + 1); // Retry
  }

  // All attempts failed
  return null;
};

export const setUpDeepgram = async (
  socket: Socket,
  userTranscripts: Map<string, string>
) => {
  const keys = await getSecretKeys();

  // STEP 1: Create a Deepgram client using the API key
  const deepgram = createClient(keys.deepgram_secret_key);

  // STEP 2: Create a live transcription connection
  const connection = deepgram.listen.live({
    model: "nova-3",
    language: "en-US",
    smart_format: true,
    interim_results: true,
    punctuate: true,
    diarize: true,
  });

  // STEP 3: Listen for events from the live transcription connection
  connection.on(LiveTranscriptionEvents.Open, () => {
    console.log("Deepgram connection opened");

    connection.on(LiveTranscriptionEvents.Close, () => {
      console.log("Connection closed.");
    });

    connection.on(LiveTranscriptionEvents.Transcript, (data) => {
      const { transcript } = data.channel.alternatives[0];
      console.log("Transcript received:", transcript);

      console.log("outside======", data.is_final);

      // Only add to the full transcript if there's content and it's a final result
      if (transcript && data.is_final) {
        console.log("inside======", data.is_final);
        // Get current transcript
        const currentFullTranscript = userTranscripts.get(socket.id) || "";

        // Append new transcript with spacing/punctuation
        const updatedTranscript = currentFullTranscript
          ? `${currentFullTranscript} ${transcript}`
          : transcript;

        // Store updated transcript
        userTranscripts.set(socket.id, updatedTranscript);
      }
    });

    connection.on(LiveTranscriptionEvents.Metadata, (data) => {
      console.log("Metadata:", data);
    });

    connection.on(LiveTranscriptionEvents.Error, (err) => {
      console.error("Deepgram error:", err);
    });
  });

  return connection;
};

/**
 * Socket.io Server Manager Class
 * Handles socket.io server initialization and event handling
 */
export class SocketIOManager {
  private io: Server;

  private userTranscripts: Map<string, string>;

  private cache: Cache;

  /**
   * Initialize Socket.io Server Manager
   * @param httpServer - HTTP Server to attach Socket.io to
   * @param cache - Cache instance for storing transcripts
   */
  constructor(
    httpServer: http.Server<
      typeof http.IncomingMessage,
      typeof http.ServerResponse
    >
  ) {
    this.userTranscripts = new Map<string, string>();
    this.cache = new Cache();

    // Initialize Socket.io server
    this.io = new Server(httpServer, {
      path: SOCKET_ROUTES.CONDUCT_INTERVIEW,
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    });

    this.setupEventHandlers();
  }

  /**
   * Setup Socket.io event handlers
   */
  private setupEventHandlers(): void {
    this.io.on("connection", async (socket: Socket) => {
      console.log("New user connected", socket.connected);
      let connection = await setUpDeepgram(socket, this.userTranscripts);

      console.log("socket id", socket.id);

      this.userTranscripts.set(socket.id, "");

      // Handle disconnect event
      socket.on("disconnect", (reason, details) => {
        this.handleDisconnect(socket, connection, reason, details);
      });

      // Handle message event
      socket.on("message", async (payload, callback) => {
        this.handleMessage(socket, connection, payload, callback).then(
          (newConnection) => {
            if (newConnection) {
              connection = newConnection;
            }
          }
        );
      });
    });
  }

  /**
   * Handle socket disconnect event
   * @param socket - Socket instance
   * @param connection - Deepgram connection
   * @param reason - Disconnect reason
   * @param details - Disconnect details
   */
  private handleDisconnect(
    socket: Socket,
    connection: any,
    reason: string,
    details: any
  ): void {
    console.log("Disconnected...");
    console.log("Reason:", reason);
    console.log("Details:", details);

    console.log("userTranscripts", this.userTranscripts.get(socket.id));

    // Get interviewId from socket query parameters
    const interviewId = socket.handshake.query.interviewId as string;

    if (interviewId) {
      // Save transcript with interviewId as key if available
      console.log(`Saving transcript for interview: ${interviewId}`);
      this.cache.set(
        `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`,
        this.userTranscripts.get(socket.id) || ""
      );
    } else {
      // Fallback to socket.id if interviewId is not available
      console.log("No interviewId found, using socket.id as fallback");
      this.cache.set(socket.id, this.userTranscripts.get(socket.id) || "");
    }

    this.userTranscripts.delete(socket.id);

    connection.requestClose();
    connection.removeAllListeners();
  }

  /**
   * Handle socket message event
   * @param socket - Socket instance
   * @param connection - Deepgram connection
   * @param payload - Message payload
   * @param callback - Message callback
   * @returns Promise with new connection if reconnected
   */
  private async handleMessage(
    socket: Socket,
    connection: any,
    payload: any,
    callback: Function
  ): Promise<any> {
    try {
      let userId = null;
      try {
        console.log("Inside socket middleware");
        const keys = await getSecretKeys();
        const jwtToken = socket.handshake.headers.authorization;
        const token = jwtToken?.split(" ")[1];

        if (!jwtToken) {
          return callback({
            success: false,
            message: "token_req",
            code: 401,
          });
        }

        const verify = jwt.verify(token, keys.token_key) as jwt.JwtPayload;
        userId = verify?.id;

        if (!verify) {
          return callback({
            success: false,
            message: "invalid_token",
            code: 401,
          });
        }
      } catch (error) {
        if (error.name === "TokenExpiredError") {
          return callback({
            success: false,
            message: "token_expired",
            code: 401,
          });
        }
        console.log("socket middleware error:", error);
        return callback({
          success: false,
          message: "invalid_token",
          code: 401,
        });
      }
      console.log("user id=========>>>>>", userId);

      if (socket.connected && payload && connection.getReadyState() === 1) {
        console.log("socket connected");
        connection.send(payload);
        console.log("Sent audio data to Deepgram");
        return null;
      }
      if (connection.getReadyState() >= 2) {
        console.log("Deepgram connection lost, reconnecting...");
        connection.requestClose();
        connection.removeAllListeners();
        const newConnection = await setUpDeepgram(socket, this.userTranscripts);
        return newConnection;
      }
      return null;
    } catch (error) {
      console.log("socket connection error:", error);
      return null;
    }
  }

  /**
   * Get the Socket.io server instance
   * @returns Socket.io Server instance
   */
  getIO(): Server {
    return this.io;
  }

  /**
   * Get user transcripts map
   * @returns Map of user transcripts
   */
  getUserTranscripts(): Map<string, string> {
    return this.userTranscripts;
  }
}
