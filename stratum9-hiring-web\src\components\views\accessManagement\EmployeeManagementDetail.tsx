"use client";
import React, { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import styles from "../../../styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import { useRouter } from "next/navigation";
import { getEmployeesByDepartment, updateEmployeeRole, updateEmployeeInterviewOrder } from "@/services/employeeService";
import { EmployeeInterface, Role } from "@/interfaces/employeeInterface";
import ChangeRoleModal from "@/components/commonModals/ChangeRoleModal";
import UpdateInterviewOrderModal from "@/components/commonModals/UpdateInterviewOrderModal";
import { toastMessageError, toastMessageSuccess } from "@/utils/helper";
import { findRole } from "@/services/roleService";
import CommonTableSkelton from "./CommonTableSkelton";
import RefreshAlertIcon from "@/components/svgComponents/RefreshAlertIcon";
import ROUTES from "@/constants/routes";

// Interface for role change modal state
interface RoleChangeModalState {
  show: boolean;
  employeeId: number | null;
  employeeName: string;
  currentRole: string;
  newRoleId: number | null;
  newRoleName: string;
}

// Interface for interview order modal state
interface InterviewOrderModalState {
  show: boolean;
  employeeId: number | null;
  employeeName: string;
  currentOrder: number;
}

const EmployeeManagementDetail = () => {
  const t = useTranslations();
  const router = useRouter();
  const { control } = useForm<{ search: string }>({ defaultValues: { search: "" } });
  const [departmentId, setDepartmentId] = useState<number | null>(null);
  const [departmentName, setDepartmentName] = useState<string>("");
  const [employees, setEmployees] = useState<EmployeeInterface[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [orderUpdated, setOrderUpdated] = useState(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const [openActionId, setOpenActionId] = useState<number | null>(null);
  const [updatingRoleId, setUpdatingRoleId] = useState<number | null>(null);
  const [updatingOrderId, setUpdatingOrderId] = useState<number | null>(null);
  const [roleChangeModal, setRoleChangeModal] = useState<RoleChangeModalState>({
    show: false,
    employeeId: null,
    employeeName: "",
    currentRole: "",
    newRoleId: null,
    newRoleName: "",
  });
  const [orderModal, setOrderModal] = useState<InterviewOrderModalState>({
    show: false,
    employeeId: null,
    employeeName: "",
    currentOrder: 0,
  });

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const id = searchParams.get("departmentId");
    const name = searchParams.get("departmentName");

    if (id) {
      setDepartmentId(+id);
    }

    if (name) {
      setDepartmentName(decodeURIComponent(name));
    }
  }, []);

  // Debounce search when departmentId or searchValue changes
  useEffect(() => {
    if (!departmentId) return;

    const debounceTimer = setTimeout(() => {
      fetchEmployees(searchValue);
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [departmentId, searchValue]);

  // Handle orderUpdated state reset separately
  useEffect(() => {
    if (orderUpdated) {
      setOrderUpdated(false);
    }
  }, [orderUpdated]);

  const fetchEmployees = async (search?: string) => {
    if (!departmentId) return;

    try {
      setIsLoading(true);
      setError(null);
      const AllRolesResponse = await findRole();
      setRoles(AllRolesResponse.data?.data || []);
      const response = await getEmployeesByDepartment(departmentId, 1, 20, search);
      if (response.data?.success && response.data.data) {
        const responseData = response?.data?.data;
        if (Array.isArray(responseData.employees)) {
          setEmployees(responseData.employees);
        }
      } else {
        toastMessageError(t("failed_load_employees"));
        setError(t("failed_load_employees"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("error_fetching_employees"));
      setError(t("unexpected_error"));
    } finally {
      setIsLoading(false);
    }
  };

  // Function to prepare role change confirmation modal
  const prepareRoleChange = (employeeId: number, roleId: number) => {
    const employee = employees.find((emp) => emp.id === employeeId);
    if (!employee) return;

    const newRole = roles.find((role) => role.id === roleId);
    if (!newRole) return;

    // Don't show modal if selecting the same role
    if (employee.selectedRole.id === roleId) return;

    const employeeName = `${employee.firstName} ${employee.lastName}`.trim() || `User ID: ${employee.userId}`;

    setRoleChangeModal({
      show: true,
      employeeId,
      employeeName,
      currentRole: employee.selectedRole.name,
      newRoleId: roleId,
      newRoleName: newRole.name,
    });
  };

  // Function to handle role change after confirmation
  const handleRoleChange = async () => {
    if (!roleChangeModal.employeeId || !roleChangeModal.newRoleId) return;

    try {
      setUpdatingRoleId(roleChangeModal.employeeId);

      const response = await updateEmployeeRole(roleChangeModal.employeeId, roleChangeModal.newRoleId);

      if (response.data?.success) {
        // Update the local state to reflect the change
        toastMessageSuccess(t(response?.data?.message ? response.data.message : "role_updated_success"));
        setEmployees((prevEmployees) =>
          prevEmployees.map((employee) => {
            if (employee.id === roleChangeModal.employeeId) {
              // Find the selected role from allRoles
              const newSelectedRole = roles.find((role) => role.id === roleChangeModal.newRoleId);

              // Update the employee's selected role and allRoles
              return {
                ...employee,
                selectedRole: newSelectedRole || employee.selectedRole,
                allRoles: roles.map((role) => ({
                  ...role,
                  selected: role.id === roleChangeModal.newRoleId,
                })),
              };
            }
            return employee;
          })
        );
      } else {
        toastMessageError(t(response?.data?.message ? response.data.message : "failed_update_role"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("error_updating_role"));
    } finally {
      setUpdatingRoleId(null);
      // Close the modal after update
      setRoleChangeModal((prev) => ({ ...prev, show: false }));
    }
  };

  // Function to cancel role change
  const cancelRoleChange = () => {
    setRoleChangeModal((prev) => ({ ...prev, show: false }));
  };

  // Function to prepare interview order update modal
  const prepareOrderChange = (employee: EmployeeInterface) => {
    setOrderModal({
      show: true,
      employeeId: employee.id,
      employeeName: `${employee.firstName || ""} ${employee.lastName || ""}`.trim(),
      currentOrder: employee.interviewOrder || 0,
    });
  };

  // Function to handle interview order update
  const handleOrderChange = async (newOrder: number) => {
    if (!orderModal.employeeId) return;

    try {
      setUpdatingOrderId(orderModal.employeeId);
      const response = await updateEmployeeInterviewOrder(orderModal.employeeId, newOrder);

      if (response.data?.success) {
        // Update the employee in the list
        setEmployees((prevEmployees) => prevEmployees.map((emp) => (emp.id === orderModal.employeeId ? { ...emp, interviewOrder: newOrder } : emp)));
        // Set flag to indicate order has been updated
        setOrderUpdated(true);
        toastMessageSuccess(t(response?.data?.message ? response.data.message : "employee_interview_order_updated"));
      } else {
        toastMessageError(t(response?.data?.message ? response.data.message : "failed_update_interview_order"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("some_error_occurred"));
    } finally {
      setUpdatingOrderId(null);
      cancelOrderChange();
    }
  };

  // Function to cancel interview order update
  const cancelOrderChange = () => {
    setOrderModal({
      show: false,
      employeeId: null,
      employeeName: "",
      currentOrder: 0,
    });
  };

  // Toggle action dropdown
  const toggleActionDropdown = (employeeId: number) => {
    setOpenActionId((prev) => (prev === employeeId ? null : employeeId));
  };

  // Determine if dropdown should show above or below based on position in table
  const shouldShowBelow = (index: number) => {
    return index < Math.floor(employees.length / 2); // Show below for first half of rows
  };

  return (
    <>
      <section className={styles.access_management}>
        <div className="container">
          <div className="row">
            <div className="col-md-12">
              <div className="button-align justify-content-between">
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>{t("employee_management_department", { department: departmentName || t("department_default") })}</h2>
                  </div>
                </div>
                <div className="button-align justify-content-end w-50">
                  <InputWrapper className="mb-0 w-100">
                    <div className="icon-align right">
                      <Textbox
                        className="form-control w-100"
                        control={control}
                        name="search"
                        type="text"
                        placeholder={t("search_placeholder")}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                          setSearchValue(e.target.value.trim());
                        }}
                      >
                        <InputWrapper.Icon>
                          <SearchIcon />
                        </InputWrapper.Icon>
                      </Textbox>
                    </div>
                  </InputWrapper>

                  <Button
                    className="primary-btn rounded-md button-sm"
                    onClick={() => {
                      router.push(
                        `${ROUTES.ROLE_EMPLOYEES.ADD_EMPLOYEE}?departmentId=${departmentId}&departmentName=${encodeURIComponent(departmentName)}`
                      );
                    }}
                  >
                    {t("employee_add_btn")}
                  </Button>
                </div>
              </div>

              <div className="table-responsive mt-5">
                <table className="table">
                  <thead>
                    <tr>
                      <th>{t("employee")}</th>
                      <th>{t("role")}</th>
                      <th>{t("interview_order")}</th>
                      <th>{t("actions")}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {isLoading ? (
                      <CommonTableSkelton Rowcount={10} ColumnCount={4} ColumnWidth="25%" />
                    ) : error ? (
                      <tr>
                        <td colSpan={4}>
                          <div className="alert alert-danger text-center mt-3">
                            <p> {error}</p>
                            <Button className="clear-btn p-0 m-auto" onClick={() => fetchEmployees(searchValue)}>
                              <RefreshAlertIcon />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ) : employees.length === 0 ? (
                      <tr>
                        <td colSpan={4} className="text-center py-4">
                          {t("no_employees_found")}
                        </td>
                      </tr>
                    ) : (
                      employees.map((employee) => (
                        <tr key={employee.id} className={employee.isAdmin ? "text-muted opacity-75 disabled-row" : ""}>
                          <td>
                            {employee.firstName && employee.lastName
                              ? `${employee.firstName} ${employee.lastName}`
                              : employee.firstName || employee.lastName || "-"}
                          </td>
                          <td>
                            <select
                              className={styles.role_select}
                              value={employee.selectedRole.id}
                              onChange={(e) => prepareRoleChange(employee.id, parseInt(e.target.value))}
                              disabled={updatingRoleId === employee.id || employee.isAdmin}
                            >
                              {roles.map((role) => (
                                <option key={role.id} value={role.id}>
                                  {role.name}
                                </option>
                              ))}
                            </select>
                            {/* {updatingRoleId === employee.id && (
                              <span className="ms-2">
                                <ClientSideLoader size="sm" />
                              </span>
                            )} */}
                          </td>
                          <td>{employee.interviewOrder}</td>
                          <td>
                            <div className="position-relative d-flex">
                              <Button className="clear-btn p-0" onClick={() => toggleActionDropdown(employee.id)}>
                                <ThreeDotsIcon />
                              </Button>

                              {openActionId === employee.id && (
                                <ul
                                  className={`${styles.custom_dropdown} ${shouldShowBelow(employees.indexOf(employee)) ? styles.show_below : ""}`}
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <li
                                    className={styles.dropdown_item}
                                    onClick={() => {
                                      setOpenActionId(null); // Close dropdown
                                      prepareOrderChange(employee);
                                    }}
                                  >
                                    {t("update_order_of_interview")}
                                  </li>
                                  <li className={styles.dropdown_item} onClick={() => setOpenActionId(null)}>
                                    {t("assign_interview")}
                                  </li>
                                  <li className={styles.dropdown_item} onClick={() => setOpenActionId(null)}>
                                    {t("remove")}
                                  </li>
                                </ul>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* Role Change Confirmation Modal */}
      {roleChangeModal.show && (
        <ChangeRoleModal
          employeeName={roleChangeModal.employeeName}
          currentRole={roleChangeModal.currentRole}
          newRole={roleChangeModal.newRoleName}
          onConfirm={handleRoleChange}
          onCancel={cancelRoleChange}
          isLoading={updatingRoleId === roleChangeModal.employeeId}
        />
      )}

      {/* Interview Order Update Modal */}
      {orderModal.show && (
        <UpdateInterviewOrderModal
          employeeName={orderModal.employeeName}
          currentOrder={orderModal.currentOrder}
          onConfirm={handleOrderChange}
          onCancel={cancelOrderChange}
          isLoading={updatingOrderId === orderModal.employeeId}
          employees={employees}
        />
      )}
    </>
  );
};

export default EmployeeManagementDetail;
