import { PERMISSIONS_COOKIES_KEY } from "@/constants/commonConstants";
import { Permission } from "@/interfaces/authInterfaces";
import { store } from "@/redux/store";
import Cookies from "js-cookie";

// Serialize specific parts of Redux state to cookies
export const syncReduxStateToCookies = (permissions?: Permission[], forceSync = false) => {
  try {
    const permissionData = Cookies.get(PERMISSIONS_COOKIES_KEY);
    if (!forceSync && permissionData) {
      return;
    }
    const state = store.getState();

    // Sync auth state to cookies (permissions are in auth state)
    if (state.auth) {
      Cookies.set(PERMISSIONS_COOKIES_KEY, JSON.stringify(permissions?.length ? permissions : state.auth.permissions), {
        expires: 4, // 4 day
        path: "/",
        sameSite: "strict",
      });
    }
    console.log("Redux state synced to cookies");
  } catch (error) {
    console.error("Error syncing Redux state to cookies:", error);
  }
};
