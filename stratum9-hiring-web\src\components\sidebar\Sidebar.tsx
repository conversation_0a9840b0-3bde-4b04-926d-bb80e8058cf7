import React from "react";
import { useRouter, usePathname } from "next/navigation";

import styles from "../../styles/sidebar.module.scss";
import HomeIcon from "../svgComponents/HomeIcon";
import CandidatesIcon from "../svgComponents/CandidatesIcon";
import CalendarIcon from "../svgComponents/CalendarIcon";
import ArchiveIcon from "../svgComponents/ArchiveIcon";
import SettingsIcon from "../svgComponents/SettingsIcon";
import LogoutIcon from "../svgComponents/LogoutIcon";
import Button from "../formElements/Button";
import ROUTES from "@/constants/routes";

function Sidebar() {
  const router = useRouter();
  const pathname = usePathname();
  return (
    <div className={styles.sidebar}>
      <ul className={styles.sidebar_list}>
        <li className={`${styles.sidebar_item} ${pathname === ROUTES.DASHBOARD ? styles.active : ""}`} onClick={() => router.push(ROUTES.DASHBOARD)}>
          <HomeIcon />
        </li>
        <li
          className={`${styles.sidebar_item} ${pathname === ROUTES.SCREEN_RESUME.CANDIDATES ? styles.active : ""}`}
          onClick={() => router.push(ROUTES.SCREEN_RESUME.CANDIDATES)}
        >
          <CandidatesIcon />
        </li>
        <li
          className={`${styles.sidebar_item} ${pathname === ROUTES.INTERVIEW.CALENDAR ? styles.active : ""}`}
          onClick={() => router.push(ROUTES.INTERVIEW.CALENDAR)}
        >
          <CalendarIcon />
        </li>
        <li
          className={`${styles.sidebar_item} ${pathname === ROUTES.JOBS.ARCHIVE ? styles.active : ""}`}
          onClick={() => router.push(ROUTES.JOBS.ARCHIVE)}
        >
          <ArchiveIcon />
        </li>
        <li
          className={`${styles.sidebar_item} ${pathname === ROUTES.JOBS.ACTIVE_JOBS ? styles.active : ""}`}
          onClick={() => router.push(ROUTES.JOBS.ACTIVE_JOBS)}
        >
          <SettingsIcon />
        </li>
      </ul>
      <Button className="clear-btn p-0">
        <LogoutIcon className={styles.logout_icon} />
      </Button>
    </div>
  );
}

export default Sidebar;
