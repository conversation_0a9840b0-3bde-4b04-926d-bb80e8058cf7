import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { QUESTION_TYPES, QuestionType } from "@/constants/commonConstants";
import { IGetInterviewSkillQuestionsResponse, IInterviewStaticInformation } from "@/interfaces/interviewInterfaces";

export interface IQuestionAnswer {
  questionId: number;
  answer: string;
}

export interface IUpdateQuestionAnswerPayload {
  questionType: QuestionType;
  questionAnswers: IQuestionAnswer[];
  stratumScore: number;
  category?: string;
  interviewerName?: string;
}

const initialState: IGetInterviewSkillQuestionsResponse & { interviewStaticInformation: IInterviewStaticInformation } = {
  roleSpecificQuestions: {},
  cultureSpecificQuestions: {},
  careerBasedQuestions: {
    questions: [],
    score: 0,
  },
  interviewStaticInformation: {
    oneToOneInterviewInstructions: [],
    videoCallInterviewInstructions: [],
    startumDescription: [],
  },
};

export const interviewSlice = createSlice({
  name: "interview",
  initialState,
  reducers: {
    setInterviewQuestions: (state, action: PayloadAction<IGetInterviewSkillQuestionsResponse>) => {
      // Handle role-specific questions
      if (action.payload.roleSpecificQuestions !== undefined) {
        state.roleSpecificQuestions = action.payload.roleSpecificQuestions;
      }

      // Handle culture-specific questions
      if (action.payload.cultureSpecificQuestions !== undefined) {
        state.cultureSpecificQuestions = action.payload.cultureSpecificQuestions;
      }

      // Handle career-based questions
      if (action.payload.careerBasedQuestions !== undefined) {
        state.careerBasedQuestions = action.payload.careerBasedQuestions;
      }
    },

    setInterviewStaticInformation: (state, action: PayloadAction<IInterviewStaticInformation>) => {
      state.interviewStaticInformation = action.payload;
    },

    updateQuestionAnswer: (state, action: PayloadAction<IUpdateQuestionAnswerPayload>) => {
      const { questionType, category, questionAnswers, stratumScore, interviewerName } = action.payload;

      // Create a Map for O(1) lookups
      const answerMap = new Map(questionAnswers.map((qa) => [qa.questionId, qa.answer]));

      switch (questionType) {
        case QUESTION_TYPES.CAREER_BASED:
          // Update answers
          state.careerBasedQuestions.questions = state.careerBasedQuestions.questions.map((question) => {
            const answer = answerMap.get(question.id);
            if (answer !== undefined) {
              return { ...question, answer };
            }
            return question;
          });

          // Update score
          state.careerBasedQuestions.score = stratumScore;
          break;

        case QUESTION_TYPES.ROLE_SPECIFIC:
          if (category) {
            // Initialize category if it doesn't exist
            if (!state.roleSpecificQuestions[category]) {
              state.roleSpecificQuestions[category] = {
                questions: [],
                score: 0,
              };
            }

            // Update answers
            state.roleSpecificQuestions[category].questions = state.roleSpecificQuestions[category].questions.map((question) => {
              const answer = answerMap.get(question.id);
              if (answer !== undefined) {
                return { ...question, answer };
              }
              return question;
            });

            // Update score and interviewer name
            state.roleSpecificQuestions[category].score = stratumScore;
            state.roleSpecificQuestions[category].interviewerName = interviewerName;
          }
          break;

        case QUESTION_TYPES.CULTURE_SPECIFIC:
          if (category) {
            // Initialize category if it doesn't exist
            if (!state.cultureSpecificQuestions[category]) {
              state.cultureSpecificQuestions[category] = {
                questions: [],
                score: 0,
              };
            }

            // Update answers
            state.cultureSpecificQuestions[category].questions = state.cultureSpecificQuestions[category].questions.map((question) => {
              const answer = answerMap.get(question.id);
              if (answer !== undefined) {
                return { ...question, answer };
              }
              return question;
            });

            // Update score and interviewer name
            state.cultureSpecificQuestions[category].score = stratumScore;
            state.cultureSpecificQuestions[category].interviewerName = interviewerName;
          }
          break;
      }
    },

    clearInterview: (state) => {
      // Reset state to initial values
      state.roleSpecificQuestions = initialState.roleSpecificQuestions;
      state.cultureSpecificQuestions = initialState.cultureSpecificQuestions;
      state.careerBasedQuestions = initialState.careerBasedQuestions;
    },
  },
});

export const { setInterviewQuestions, updateQuestionAnswer, clearInterview, setInterviewStaticInformation } = interviewSlice.actions;

export default interviewSlice.reducer;
