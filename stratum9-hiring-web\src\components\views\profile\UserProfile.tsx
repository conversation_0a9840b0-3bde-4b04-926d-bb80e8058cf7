"use client";
import Button from "@/components/formElements/Button";
import Link from "next/link";
import style from "../../../styles/conductInterview.module.scss";
import Image from "next/image";
import candidateProfile from "../../../../public/assets/images/doctor-strange.png";
import { useState, useEffect, useCallback } from "react";
import { getMyProfile } from "@/services/userProfileService";
import { formatDate, toastMessageError } from "@/utils/helper";
import { useTranslations } from "next-intl";
import { useDispatch, useSelector } from "react-redux";
import { selectProfileData, updateUserProfileData, selectRole, selectDepartment } from "@/redux/slices/authSlice";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import EditIcon from "@/components/svgComponents/EditIcon";
import EditProfileModal from "@/components/commonModals/EditProfileModal";
const UserProfile = () => {
  const [selectedTab, setSelectedTab] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const t = useTranslations();
  const dispatch = useDispatch();

  // Get user profile data from Redux store
  const userProfile = useSelector(selectProfileData);
  const userRole = useSelector(selectRole);
  const userDepartment = useSelector(selectDepartment);
  const [isLoading, setIsLoading] = useState(false);

  const fetchUserProfile = useCallback(async () => {
    try {
      setIsLoading(true);
      setLoadError(null);
      const response = await getMyProfile();

      // Based on the provided response structure
      const responseData = response?.data;

      if (responseData?.success && responseData?.data) {
        // Extract the actual user profile data and store in Redux
        dispatch(
          updateUserProfileData({
            first_name: responseData.data.firstName,
            last_name: responseData.data.lastName,
            image: responseData.data.image,
          })
        );
      } else {
        const errorMessage = responseData?.message || "failed_to_load_profile";
        toastMessageError(t(errorMessage));
        setLoadError(t(errorMessage));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("error_fetching_profile"));
      setLoadError(t("error_fetching_profile"));
    } finally {
      setIsLoading(false);
    }
  }, [t, dispatch]);

  // Always fetch profile data when component mounts
  useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  const handleTabClick = () => {
    setSelectedTab(!selectedTab);
  };

  // Render the main content of the profile page
  const renderMainContent = () => (
    <div className={style.conduct_interview_page}>
      <div className="container">
        <div className="common-page-header">
          <div className="breadcrumb">
            <Link href="/">Home </Link>
            <Link href="/">Profile</Link>
          </div>
          <div className="common-page-head-section d-flex justify-content-between align-items-center">
            <div className="main-heading">
              <h2>
                User <span>Profile</span>
              </h2>
            </div>
            <div className="d-flex gap-3">
              <Link
                href="#"
                className="d-flex align-items-center gap-2 text-decoration-none"
                onClick={(e) => {
                  e.preventDefault();
                  setShowEditModal(true);
                }}
              >
                <EditIcon className="" />
                <span>{t("edit_profile")}</span>
              </Link>
              <Link href="#" className="d-flex align-items-center gap-2 text-decoration-none">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M12.6667 2H3.33333C2.59695 2 2 2.59695 2 3.33333V12.6667C2 13.403 2.59695 14 3.33333 14H12.6667C13.403 14 14 13.403 14 12.6667V3.33333C14 2.59695 13.403 2 12.6667 2Z"
                    stroke="#436EB6"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M5.33337 8.00004L7.33337 10L11.3334 6.00004"
                    stroke="#436EB6"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                <span>{t("my_interviews")}</span>
              </Link>
            </div>
          </div>
        </div>
        <div className="inner-section profile-section">
          <div className="candidate-profile user-profile">
            {isLoading ? (
              <div className="position-relative">
                <div style={{ width: "100px", height: "100px", borderRadius: "12px", overflow: "hidden" }}>
                  <Skeleton width={100} height={100} />
                </div>
              </div>
            ) : (
              <div className="position-relative">
                <Image
                  src={userProfile?.image && userProfile.image !== "" ? userProfile.image : candidateProfile}
                  alt="profile image"
                  className="candidate-image"
                  width={100}
                  height={100}
                />
              </div>
            )}
            <div>
              {isLoading ? (
                <>
                  <Skeleton width={150} height={25} />
                  <Skeleton width={120} height={20} />
                </>
              ) : (
                <>
                  <h3 className="candidate-name">{userProfile ? `${userProfile.first_name} ${userProfile.last_name}`.trim() : "User"}</h3>
                  <h3 className="candidate-role">{userRole?.roleName || "Role Not Assigned"}</h3>
                </>
              )}
            </div>
          </div>
          <div className="candidate-info user-info-md">
            {loadError ? (
              <div className="error-message">
                <p>{loadError}</p>
                <Button className="primary-btn" onClick={fetchUserProfile}>
                  Retry
                </Button>
              </div>
            ) : (
              <div className="info-container">
                <div className="info-item">
                  <p className="info-title">{t("email_address")}</p>
                  {isLoading ? <Skeleton width={150} /> : <p className="info-value">{userProfile?.email}</p>}
                </div>
                <div className="info-item">
                  <p className="info-title">{t("organization_name")}</p>
                  {isLoading ? <Skeleton width={150} /> : <p className="info-value">{userProfile?.organizationName}</p>}
                </div>
                <div className="info-item">
                  <p className="info-title">{t("organization_code")}</p>
                  {isLoading ? <Skeleton width={150} /> : <p className="info-value">{userProfile?.organizationCode}</p>}
                </div>
                <div className="info-item">
                  <p className="info-title">{t("department")}</p>
                  {isLoading ? <Skeleton width={150} /> : <p className="info-value">{userDepartment?.departmentName || "Not Available"}</p>}
                </div>
                <div className="info-item">
                  <p className="info-title">{t("account_created_on")}</p>
                  {isLoading ? (
                    <Skeleton width={150} />
                  ) : (
                    <p className="info-value">{userProfile?.createdTs ? formatDate(userProfile.createdTs) : "Not Available"}</p>
                  )}
                </div>
              </div>
            )}
          </div>
          <div className="common-tab mb-5 mt-5">
            <li className={selectedTab ? "py-3 active" : "py-3"} onClick={handleTabClick}>
              Billing History
            </li>
            <li className={!selectedTab ? "py-3 active" : "py-3"} onClick={handleTabClick}>
              Subscription Plan
            </li>
          </div>
          {selectedTab && (
            <div className="table-responsive">
              <table className="table">
                <thead>
                  <tr>
                    <th>Invoice No.</th>
                    <th>Invoice Date</th>
                    <th>Plan</th>
                    <th>Amount</th>
                    <th>Duration</th>
                    <th>Transaction ID</th>
                    <th>Payment Date</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>INV-2024-001</td>
                    <td>2024-05-01</td>
                    <td>Professional</td>
                    <td>$99</td>
                    <td>Monthly</td>
                    <td>TXN-5A8B3C9D</td>
                    <td>2024-05-01</td>
                    <td>
                      <span className="text-danger">Overdue</span>
                    </td>
                  </tr>
                  <tr>
                    <td>INV-2024-002</td>
                    <td>2024-06-01</td>
                    <td>Professional</td>
                    <td>$99</td>
                    <td>Monthly</td>
                    <td>TXN-7E2F1G4H</td>
                    <td>2024-06-01</td>
                    <td>
                      <span className="text-success">Paid</span>
                    </td>
                  </tr>
                  <tr>
                    <td>INV-2024-003</td>
                    <td>2024-07-01</td>
                    <td>Enterprise</td>
                    <td>$299</td>
                    <td>Monthly</td>
                    <td>TXN-9I0J5K6L</td>
                    <td>2024-07-01</td>
                    <td>
                      <span className="text-success">Paid</span>
                    </td>
                  </tr>
                  <tr>
                    <td>INV-2024-004</td>
                    <td>2024-04-01</td>
                    <td>Starter</td>
                    <td>$49</td>
                    <td>Monthly</td>
                    <td>TXN-3M4N5O7P</td>
                    <td>2024-04-01</td>
                    <td>
                      <span className="text-success">Paid</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          )}
          {!selectedTab && (
            <div className="user-subscription-plan">
              <div className="section-heading">
                <h2>Current Plan</h2>
              </div>
              <div className="row">
                <div className="col-md-4">
                  <div className="plan-info-card">
                    <div className="plan-header">
                      <h3 className="plan-title">Professional</h3>
                      <span className="plan-status">Active</span>
                    </div>
                    <div className="plan-details">
                      <p className="plan-price">$4.99 Monthly</p>
                      <p className="plan-access">Full Access</p>
                    </div>
                    <div className="plan-actions">
                      <Button className="primary-btn rounded-md w-100">Change Plan</Button>
                      <Button className="dark-outline-btn rounded-md w-100">Cancel Plan</Button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="button-align mt-5 pt-5">
                <Button className="primary-btn rounded-md" onClick={() => setShowEditModal(true)}>
                  Edit Profile
                </Button>
                <Button className="dark-outline-btn rounded-md">My Interviews</Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <>
      {renderMainContent()}
      {showEditModal && (
        <EditProfileModal
          onClickCancel={() => setShowEditModal(false)}
          onSubmitSuccess={() => {
            setShowEditModal(false);
            setIsLoading(true); // Set loading state to true to show skeleton
            // Add a small delay before fetching to ensure skeleton is visible
            setTimeout(() => {
              fetchUserProfile(); // Refresh user profile data after update
            }, 500);
          }}
        />
      )}
    </>
  );
};

export default UserProfile;
