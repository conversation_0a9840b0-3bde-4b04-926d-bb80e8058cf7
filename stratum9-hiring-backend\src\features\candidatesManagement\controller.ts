// src/features/candidatesManagement/controller.ts

import { Request, Response } from "express";
import { DEFAULT_LIMIT } from "../../utils/constants";
import CandidateApplicationService from "./services";

// Get all candidates
export const getCandidatesController = async (req: Request, res: Response) => {
  try {
    const orgId = Number(req.orgId);
    const jobId = req.query.jobId ? Number(req.query.jobId) : undefined;
    const searchStr = req.query.searchStr ? String(req.query.searchStr) : "";
    const offset = req.query.page ? Number(req.query.page) : 0;
    const limit = req.query.limit ? Number(req.query.limit) : DEFAULT_LIMIT;

    let isActive: boolean | undefined;

    const candidates = await CandidateApplicationService.getAllCandidates(
      orgId,
      jobId,
      isActive,
      searchStr,
      offset,
      limit
    );

    return res.status(200).json({
      success: true,
      data: candidates,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Update candidate application status

export const archiveActiveApplication = async (req: Request, res: Response) => {
  const applicationId = Number(req.params.applicationId);
  const orgId = Number(req.orgId);

  const { isActive, reason } = req.body;

  try {
    const result = await CandidateApplicationService.archiveActiveApplication(
      applicationId,
      orgId,
      isActive,
      reason
    );

    if (!result.success) {
      return res.status(404).json(result);
    }
    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Get top candidates

export const getTopCandidatesController = async (
  req: Request,
  res: Response
) => {
  try {
    const orgId = Number(req.orgId);
    const jobId = Number(req.query.jobId);

    const candidates = await CandidateApplicationService.getTopCandidates(
      orgId,
      jobId
    );

    return res.status(200).json({
      ...candidates,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Promote/Demote candidate

export const promoteDemoteCandidateController = async (
  req: Request,
  res: Response
) => {
  try {
    const { candidateId, applicationId, action } = req.body;

    // Uncomment and use when actual service is ready
    const data = await CandidateApplicationService.promoteDemoteCandidate(
      candidateId,
      applicationId,
      action
    );

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

export const getCandidateDetailsController = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await CandidateApplicationService.getCandidateDetails(
      Number(req.query.candidateId),
      req.orgId
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

export const addApplicantAdditionalInfoController = async (
  req: Request,
  res: Response
) => {
  try {
    const result = await CandidateApplicationService.addApplicantAdditionalInfo(
      req.orgId,
      req.body
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json(error);
  }
};

// Controller for updating job application status
export const updateJobApplicationStatusController = async (
  req: Request,
  res: Response
) => {
  try {
    const jobApplicationId = Number(req.params.jobApplicationId);
    const { status } = req.body;

    const result = await CandidateApplicationService.updateJobApplicationStatus(
      jobApplicationId,
      status
    );

    return res.status(200).json({
      ...result,
      code: 200,
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: "Failed to update job application status",
      error: error instanceof Error ? error.message : "Unknown error",
      code: 500,
    });
  }
};
