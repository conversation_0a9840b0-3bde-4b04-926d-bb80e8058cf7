/* eslint-disable prefer-destructuring */
/* eslint-disable no-unused-vars */
import { NextFunction, Response, Request } from "express";
import * as jwt from "jsonwebtoken";
import { getSecretKeys } from "../config/awsConfig";
import Cache from "../db/cache";
import { REDIS_KEYS, PLATFORM } from "../utils/constants";

export const auth = async (req: Request, res: Response, next: NextFunction) => {
  const keys = await getSecretKeys();
  const jwtToken =
    <string>req.headers.auth ||
    <string>req.headers.authorization ||
    <string>req.headers.Authorization;
  const tokenParts = jwtToken?.split(" ");

  try {
    const startTime = new Date().getTime();

    // console.log("in time====>>>>>", startTime);
    if (!jwtToken) {
      return res.status(401).send({
        message: "token_req_msg",
        success: false,
        error: "token_req",
      });
    }

    if (tokenParts.length !== 2 || tokenParts[0].toLowerCase() !== "bearer") {
      return res.status(401).send({
        message: "invalid_token_msg",
        success: false,
        error: "invalid_token",
      });
    }

    const verify = jwt.verify(tokenParts[1], keys.token_key) as jwt.JwtPayload;
    if (!verify) {
      return res.status(401).send({
        message: "invalid_token_msg",
        success: false,
        error: "invalid_token",
      });
    }

    if (verify.platform !== PLATFORM.STRATUM9_INNERVIEW) {
      return res.status(401).send({
        message: "unauthorized_msg",
        success: false,
        error: "unauthorized",
      });
    }

    req.userId = verify.id;
    req.roleId = verify.roleId;
    req.orgId = verify.orgId;
    req.departmentId = verify.departmentId;
    req.token = tokenParts[1];

    const cache = new Cache();
    const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
      "{userId}",
      String(req.userId)
    );

    const activeTokens = await cache.lRange(sessionKey);

    // eslint-disable-next-line no-console
    // console.log("activeTokens===>>>", activeTokens);

    console.log("total time=====>>>>>>>", new Date().getTime() - startTime);

    if (!activeTokens.includes(tokenParts[1])) {
      return res.status(401).send({
        message: "invalid_token_msg",
        success: false,
        error: "invalid_token",
      });
    }

    next();
  } catch (error) {
    const { userId } = req.params;

    const cache = new Cache();
    const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
      "{userId}",
      String(userId)
    );

    await cache.lRem(sessionKey, tokenParts[1]);

    if (error.name === "TokenExpiredError") {
      return res.status(401).send({
        message: "token_expired_msg",
        success: false,
        error: "token_expired",
      });
    }
    return res.status(401).send({
      message: "token_invalid_msg",
      success: false,
      error: "invalid_token",
    });
  }
  return null;
};

export default auth;
