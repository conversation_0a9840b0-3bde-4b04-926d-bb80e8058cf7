"use client";
import React, { useCallback, useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSelector } from "react-redux";
import { AuthState } from "@/redux/slices/authSlice";
import { useTranslations } from "next-intl";

import { syncReduxStateToCookies } from "@/utils/syncReduxToCookies";
import Logo from "../../../public/assets/images/logo.svg";
import downArrow from "../../../public/assets/images/down-arrow.svg";
import User from "../../../public/assets/images/user.png";
import styles from "@/styles/header.module.scss";
import NotificationIcon from "../svgComponents/Notification";
import { logout } from "@/utils/helper";
import { usePathname, useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { selectProfileData, setPermissions } from "@/redux/slices/authSlice";
import { getUserPermissions } from "@/services/authServices";
import Profile from "../../../public/assets/images/Profile.svg";

// Interface definitions moved to authServices.ts
import DataSecurityIcon from "../svgComponents/dataSecurityIcon";
import ROUTES from "@/constants/routes";
import Button from "../formElements/Button";
import { IUserData } from "@/interfaces/authInterfaces";

const Header = () => {
  const [dropdown, SetDropdown] = useState(false);
  const userProfile: IUserData | null = useSelector(selectProfileData);

  const path = usePathname();
  const dispatch = useDispatch();
  const authData = useSelector((state: { auth: AuthState }) => state.auth.authData);
  const t = useTranslations("header");
  const tCommon = useTranslations("common");
  const pathname = usePathname();
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  const navigate = useRouter();
  const [showNotifications, setShowNotifications] = useState(false);

  // Handle clicks outside of dropdown to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        SetDropdown(false);
      }
    };

    // Add event listener when dropdown is open
    if (dropdown) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    // Clean up event listener
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dropdown]);

  // Toggle dropdown visibility
  const MenuDropdown = () => {
    SetDropdown(!dropdown);
  };

  // Function to fetch permissions using the authServices
  const fetchPermissions = useCallback(async () => {
    try {
      const response = await getUserPermissions();

      // Only update Redux store when success is true
      if (response.data?.success) {
        dispatch(setPermissions(response.data.data.rolePermissions));
        // Sync Redux state to cookies after updating permissions
        syncReduxStateToCookies(response.data.data.rolePermissions, true);
      } else {
        console.log("Permission fetch unsuccessful:", response.data?.message);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
    }
  }, [path, dispatch]);

  // when someone manually removes localStor

  // useEffect(() => {
  //   logoutUser();
  // }, []);

  // Sync Redux state to cookies after mounting component
  useEffect(() => {
    syncReduxStateToCookies();
  }, []);

  useEffect(() => {
    // Check if this is first mount or a genuine route change
    fetchPermissions();
  }, [path, dispatch, fetchPermissions]);

  /**
   * Logs out the user if the access token is invalid.
   * If the access token is invalid, it logs out the user and shows a toast message.
   */

  // const logoutUser = async () => {
  //   const token = getAccessToken();
  //   if (!token) {
  //     onHandleLogout();
  //     toast.dismiss();
  //     toastMessageError(t("session_expired"));
  //   }
  // };

  const onHandleLogout = async () => {
    await logout(authData?.id);

    if (typeof window !== "undefined") {
      window.location.reload();
    }
  };

  return (
    <>
      <header
        className={styles.header}
        // className={`${styles.header} ${isVisible ? "" : `${styles.hidden}`}`}
      >
        <nav className="navbar navbar-expand-sm">
          <div className="container">
            <Link className="navbar-brand" href={ROUTES.HOME}>
              <Image src={Logo} alt="logo" width={640} height={320} className={styles.logo} />
            </Link>
            {/* <Button className="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#collapsibleNavbar">
              <span className="navbar-toggler-icon"></span>
            </Button> */}
            <div className={`collapse navbar-collapse justify-content-end ${styles.navbar_content}`} id="collapsibleNavbar">
              <ul className={`navbar-nav ${styles.navbar_links}`}>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${pathname === ROUTES.JOBS.GENERATE_JOB || pathname === ROUTES.JOBS.CAREER_BASED_SKILLS || pathname === ROUTES.JOBS.CULTURE_BASED_SKILLS || pathname === ROUTES.JOBS.ROLE_BASED_SKILLS || pathname === ROUTES.JOBS.EDIT_SKILLS || pathname === ROUTES.JOBS.JOB_EDITOR || pathname === ROUTES.JOBS.HIRING_TYPE ? styles.active : ""}`}
                    href={ROUTES.JOBS.HIRING_TYPE}
                  >
                    {t("job_requirement_generations")}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${pathname === ROUTES.JOBS.ACTIVE_JOBS || pathname?.startsWith(ROUTES.SCREEN_RESUME.MANUAL_CANDIDATE_UPLOAD) || pathname?.startsWith(ROUTES.SCREEN_RESUME.CANDIDATE_QUALIFICATION) ? styles.active : ""}`}
                    href={ROUTES.JOBS.ACTIVE_JOBS}
                  >
                    {t("resume_screening")}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link className="nav-link" href="#">
                    {t("conduct_interview")}
                  </Link>
                </li>
                <li className="nav-item">
                  <Link
                    className={`nav-link ${pathname === ROUTES.DASHBOARD || pathname === ROUTES.SCREEN_RESUME.CANDIDATES || pathname === ROUTES.JOBS.ARCHIVE ? styles.active : ""}`}
                    href={ROUTES.DASHBOARD}
                  >
                    {tCommon("hm_dashboard")}
                  </Link>
                </li>
              </ul>

              <div className={styles.header_right}>
                <NotificationIcon hasNotification={true} onClick={() => setShowNotifications(!showNotifications)} />
                <div className={`dropdown ${styles.user_drop}`} ref={dropdownRef}>
                  <button type="button" className={`dropdown-toggle ${styles.user_drop_btn}`} data-bs-toggle="dropdown" onClick={MenuDropdown}>
                    <div className={`${styles.circle_img}`}>
                      <Image src={userProfile?.image || User} alt="Profile" width={100} height={100} />
                    </div>
                    <div className={styles.admin_info}>
                      <h5>{`${userProfile?.first_name}`}</h5>
                    </div>
                    <Image src={downArrow} alt="downArrow" style={{ rotate: `${dropdown ? "180deg" : "0deg"}` }} />
                  </button>
                  {dropdown && (
                    <ul className={styles.dropdown_menu}>
                      <li>
                        <Image src={Profile} alt="userPlaceholder" />
                        <span
                          onClick={() => {
                            navigate.push(ROUTES.PROFILE.MY_PROFILE);
                            SetDropdown(false);
                          }}
                        >
                          {t("my_profile")}
                        </span>
                      </li>
                      <li>
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none">
                          <path
                            d="M14.9453 1.25C13.5778 1.24998 12.4754 1.24996 11.6085 1.36652C10.7084 1.48754 9.95049 1.74643 9.34858 2.34835C8.82364 2.87328 8.5584 3.51836 8.41917 4.27635C8.28388 5.01291 8.25799 5.9143 8.25196 6.99583C8.24966 7.41003 8.58358 7.74768 8.99779 7.74999C9.412 7.7523 9.74965 7.41838 9.75195 7.00418C9.75804 5.91068 9.78644 5.1356 9.89449 4.54735C9.9986 3.98054 10.1658 3.65246 10.4092 3.40901C10.686 3.13225 11.0746 2.9518 11.8083 2.85315C12.5637 2.75159 13.5648 2.75 15.0002 2.75H16.0002C17.4356 2.75 18.4367 2.75159 19.1921 2.85315C19.9259 2.9518 20.3144 3.13225 20.5912 3.40901C20.868 3.68577 21.0484 4.07435 21.1471 4.80812C21.2486 5.56347 21.2502 6.56459 21.2502 8V16C21.2502 17.4354 21.2486 18.4365 21.1471 19.1919C21.0484 19.9257 20.868 20.3142 20.5912 20.591C20.3144 20.8678 19.9259 21.0482 19.1921 21.1469C18.4367 21.2484 17.4356 21.25 16.0002 21.25H15.0002C13.5648 21.25 12.5637 21.2484 11.8083 21.1469C11.0746 21.0482 10.686 20.8678 10.4092 20.591C10.1658 20.3475 9.9986 20.0195 9.89449 19.4527C9.78644 18.8644 9.75804 18.0893 9.75195 16.9958C9.74965 16.5816 9.412 16.2477 8.99779 16.25C8.58358 16.2523 8.24966 16.59 8.25196 17.0042C8.25799 18.0857 8.28388 18.9871 8.41917 19.7236C8.5584 20.4816 8.82364 21.1267 9.34858 21.6517C9.95049 22.2536 10.7084 22.5125 11.6085 22.6335C12.4754 22.75 13.5778 22.75 14.9453 22.75H16.0551C17.4227 22.75 18.525 22.75 19.392 22.6335C20.2921 22.5125 21.0499 22.2536 21.6519 21.6517C22.2538 21.0497 22.5127 20.2919 22.6337 19.3918C22.7503 18.5248 22.7502 17.4225 22.7502 16.0549V7.94513C22.7502 6.57754 22.7503 5.47522 22.6337 4.60825C22.5127 3.70814 22.2538 2.95027 21.6519 2.34835C21.0499 1.74643 20.2921 1.48754 19.392 1.36652C18.525 1.24996 17.4227 1.24998 16.0551 1.25H14.9453Z"
                            fill="#191919"
                          />
                          <path
                            d="M15 11.25C15.4142 11.25 15.75 11.5858 15.75 12C15.75 12.4142 15.4142 12.75 15 12.75H4.02744L5.98809 14.4306C6.30259 14.7001 6.33901 15.1736 6.06944 15.4881C5.79988 15.8026 5.3264 15.839 5.01191 15.5694L1.51191 12.5694C1.34567 12.427 1.25 12.2189 1.25 12C1.25 11.7811 1.34567 11.573 1.51191 11.4306L5.01191 8.43056C5.3264 8.16099 5.79988 8.19741 6.06944 8.51191C6.33901 8.8264 6.30259 9.29988 5.98809 9.56944L4.02744 11.25H15Z"
                            fill="#191919"
                          />
                        </svg>
                        <span onClick={() => onHandleLogout()}>Logout</span>
                      </li>
                    </ul>
                  )}
                </div>
              </div>
            </div>
          </div>
        </nav>
      </header>
      {showNotifications ? (
        <div className="notifications">
          <div className="header-content">
            <h3>Notifications</h3>
            <Button className="clear-btn p-0">Clear All</Button>
          </div>
          <div className="read-btns">
            <Button className="primary-btn">All</Button>
            <Button className="grey-btn">Unread</Button>
          </div>
          <div className="notification-wrapper">
            <div className="notification-item unread">
              <h4>Upcoming Interview for Operations Admin</h4>
              <p>You have an interview scheduled on May 24, 2025 at 10:00 AM. </p>
              <p className="time">May 22, 2025 | 03:36 PM</p>
            </div>
            <div className="notification-item">
              <h4>Upcoming Interview for Operations Admin</h4>
              <p>You have an interview scheduled on May 24, 2025 at 10:00 AM. </p>
              <p className="time">May 22, 2025 | 03:36 PM</p>
            </div>
            <div className="notification-item">
              <h4>Upcoming Interview for Operations Admin</h4>
              <p>You have an interview scheduled on May 24, 2025 at 10:00 AM. </p>
              <p className="time">May 22, 2025 | 03:36 PM</p>
            </div>
          </div>
        </div>
      ) : null}

      {/* common pages information box for  Job Requirement Generation page */}
      {pathname === ROUTES.JOBS.GENERATE_JOB && (
        <div className="information-box">
          <DataSecurityIcon />
          <p>We prioritize your data’s security. With encryption at every step, your privacy is secure and protected.</p>
        </div>
      )}
    </>
  );
};

export default Header;
