import type { Metadata } from "next";
import "../../node_modules/bootstrap/dist/css/bootstrap.css";
import "../styles/style.scss";

import { NextIntlClientProvider } from "next-intl";
import { getLocale } from "next-intl/server";
import { Toaster } from "react-hot-toast";

// Import Redux Provider
import ReduxProvider from "@/redux/ReduxProvider";
import HeaderWrapper from "@/components/header/HeaderWrapper";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  return (
    <html lang="en">
      <body>
        <ReduxProvider>
          <NextIntlClientProvider locale={locale}>
            <HeaderWrapper />
            {children}
            <Toaster position="top-right" />
          </NextIntlClientProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
