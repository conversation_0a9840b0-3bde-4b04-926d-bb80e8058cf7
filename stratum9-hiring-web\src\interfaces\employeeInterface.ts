import { Department } from "./departmentInterface";

export interface EmployeeForm {
  firstName: string;
  lastName: string;
  email: string;
  department: number;
  role: number;
}

export interface EmployeeStatus {
  email: string;
  status: boolean;
  message: string;
}

export interface Role {
  id: number;
  name: string;
  isDefaultRole: boolean;
}

export interface DepartmentDetail {
  id: number;
  name: string;
  isDefaultDepartment: boolean;
}

export interface EmployeeInterface {
  employees?: {
    id: number;
    userId: number;
    firstName: string;
    lastName: string;
    interviewOrder: number;
    department: DepartmentDetail;
    selectedRole: Role;
    isAdmin: boolean;
  }[];
  id: number;
  userId: number;
  firstName: string;
  lastName: string;
  interviewOrder: number;
  department: DepartmentDetail;
  selectedRole: Role;
  isAdmin: boolean;
}

export interface Pagination {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AddEmployeeResponseData {
  addedCount?: number;
  employees?: EmployeeForm[] | EmployeeInterface[];
  errors?: string[];
  employeeStatuses?: EmployeeStatus[];
  departments?: Department[];
  id?: number;
  name?: string;
  associatedOrganizationId?: number;
  pagination?: Pagination;
}

export interface UserRoleForm {
  name: string;
}
