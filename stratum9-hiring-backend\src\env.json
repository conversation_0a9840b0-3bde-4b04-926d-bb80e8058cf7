{"local": {"env": "local", "port": 3001, "profile": "stratum9", "secretManagerKey": "stratum9_mobile_app-secret-keys-stratum9", "region": "us-east-1", "generate_interview_questions_lambda": "stratum-cron-jobs-development-generateInterviewQuestions", "s3_bucket": "s9-interview-assets", "openai_api_key": "********************************************************************************************************************************************************************"}, "development": {"env": "development", "port": 3001, "region": "us-east-1", "profile": "stratum9", "secretManagerKey": "stratum9_mobile_app-secret-keys-stratum9", "s3_bucket": "s9-interview-assets", "openai_api_key": "********************************************************************************************************************************************************************"}, "production": {"env": "production", "port": 3001, "region": "us-east-1", "profile": "stratum9", "secretManagerKey": "prod-secret-keys-stratum9", "s3_bucket": "s9-interview-assets-prod"}}